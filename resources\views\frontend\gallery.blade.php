@extends('frontend.layouts.frontend')

@section('title','Gallery')

@section('content')
<!--Start breadcrumb area paroller-->
<section class="breadcrumb-area">
    <div class="breadcrumb-area-bg" style="background-image: url(assets/images/breadcrumb/breadcrumb-1.jpg);"></div>
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="inner-content text-center">
                    <div class="title">
                       <h2>Gallery</h2>
                    </div>
                    <div class="breadcrumb-menu">
                        <ul>
                            <li><a href="/">Home</a></li>
                            <li><i class="fa fa-angle-right" aria-hidden="true"></i></li>
                            <li class="active">Gallery</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End breadcrumb area-->



<!--Start Project Style2 Area-->
@php use Illuminate\Support\Str; @endphp
<section class="project-style2-area">
    <div class="container">
        <div class="project-menu-box text-center wow fadeInUp" data-wow-delay="100ms" data-wow-duration="1500ms">
            <ul class="project-filter clearfix post-filter has-dynamic-filters-counter">
                <li data-filter="all" class="active"><span class="filter-text">All</span></li>
                @foreach($gallery_categories as $category)
                    <li data-filter="{{ Str::slug($category->name) }}">
                        <span class="filter-text">{{ $category->name }}</span>
                    </li>
                @endforeach
            </ul>
        </div>
        <div class="row filter-layout masonary-layout">
            <!--Start Single Gallery Item-->
            @foreach($images as $index => $image)
            @if(!empty($image->image) && $image->category)
            <div class="col-xl-4 col-lg-6 col-md-6 filter-item {{ Str::slug($image->category->name) }}" style="{{ $index >= 6 ? 'display: none;' : '' }}">
                <div class="single-gallery-item">
                    <div class="img-holder">
                        <img src="{{asset('storage/'. $image->image)}}" alt="Awesome Image">
                        <div class="overlay-content text-center">
                            <div class="zoom-button">
                                <a class="lightbox-image" data-fancybox="gallery" href="assets/images/portfolio/portfolio-v2-1.jpg">
                                    <i class="flaticon-plus"></i>
                                </a>
                            </div>
                            <div class="inner">
                                <div class="border-box"></div>
                                 <h6>{{$image->category->name}}</h6>
                                <h3><a href="">{{$image->{'caption_'. app()->getLocale()} }}</a></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
            @endforeach
            {{-- <!--End Single Gallery Item-->
            <!--Start Single Gallery Item-->
            <div class="col-xl-4 col-lg-6 col-md-6 filter-item delivery repair">
                <div class="single-gallery-item">
                    <div class="img-holder">
                        <img src="assets/images/portfolio/portfolio-v2-2.jpg" alt="Awesome Image">
                        <div class="overlay-content text-center">
                            <div class="zoom-button">
                                <a class="lightbox-image" data-fancybox="gallery" href="assets/images/portfolio/portfolio-v2-2.jpg">
                                    <i class="flaticon-plus"></i>
                                </a>
                            </div>
                            <div class="inner">
                                <div class="border-box"></div>
                                <h6>Maintenance</h6>
                                <h3><a href="project-details.html">Top Level Purified</a></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Single Gallery Item-->
            <!--Start Single Gallery Item-->
            <div class="col-xl-4 col-lg-6 col-md-6 filter-item maintenance testing">
                <div class="single-gallery-item">
                    <div class="img-holder">
                        <img src="assets/images/portfolio/portfolio-v2-3.jpg" alt="Awesome Image">
                        <div class="overlay-content text-center">
                            <div class="zoom-button">
                                <a class="lightbox-image" data-fancybox="gallery" href="assets/images/portfolio/portfolio-v2-3.jpg">
                                    <i class="flaticon-plus"></i>
                                </a>
                            </div>
                            <div class="inner">
                                <div class="border-box"></div>
                                <h6>Maintenance</h6>
                                <h3><a href="project-details.html">Top Level Purified</a></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Single Gallery Item-->

            <!--Start Single Gallery Item-->
            <div class="col-xl-4 col-lg-6 col-md-6 filter-item delivery repair">
                <div class="single-gallery-item">
                    <div class="img-holder">
                        <img src="assets/images/portfolio/portfolio-v2-4.jpg" alt="Awesome Image">
                        <div class="overlay-content text-center">
                            <div class="zoom-button">
                                <a class="lightbox-image" data-fancybox="gallery" href="assets/images/portfolio/portfolio-v2-4.jpg">
                                    <i class="flaticon-plus"></i>
                                </a>
                            </div>
                            <div class="inner">
                                <div class="border-box"></div>
                                <h6>Maintenance</h6>
                                <h3><a href="project-details.html">Top Level Purified</a></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Single Gallery Item-->
            <!--Start Single Gallery Item-->
            <div class="col-xl-4 col-lg-6 col-md-6 filter-item maintenance testing">
                <div class="single-gallery-item">
                    <div class="img-holder">
                        <img src="assets/images/portfolio/portfolio-v2-5.jpg" alt="Awesome Image">
                        <div class="overlay-content text-center">
                            <div class="zoom-button">
                                <a class="lightbox-image" data-fancybox="gallery" href="assets/images/portfolio/portfolio-v2-5.jpg">
                                    <i class="flaticon-plus"></i>
                                </a>
                            </div>
                            <div class="inner">
                                <div class="border-box"></div>
                                <h6>Maintenance</h6>
                                <h3><a href="project-details.html">Top Level Purified</a></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Single Gallery Item-->
            <!--Start Single Gallery Item-->
            <div class="col-xl-4 col-lg-6 col-md-6 filter-item delivery repair">
                <div class="single-gallery-item">
                    <div class="img-holder">
                        <img src="assets/images/portfolio/portfolio-v2-6.jpg" alt="Awesome Image">
                        <div class="overlay-content text-center">
                            <div class="zoom-button">
                                <a class="lightbox-image" data-fancybox="gallery" href="assets/images/portfolio/portfolio-v2-6.jpg">
                                    <i class="flaticon-plus"></i>
                                </a>
                            </div>
                            <div class="inner">
                                <div class="border-box"></div>
                                <h6>Maintenance</h6>
                                <h3><a href="project-details.html">Top Level Purified</a></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Single Gallery Item--> --}}
        </div>

        <div class="row">
            <div class="col-xl-12 text-center">
                <div class="project-style2-load-more-button">
                    <a class="btn-one" href="portfolio-1.html">
                        <div class="round"></div>
                        <span class="txt">Load More</span>
                    </a>
                </div>
            </div>
        </div>

    </div>
</section>
<!--End Project Style2 Area-->
<script>
document.addEventListener("DOMContentLoaded", function () {
    const filterButtons = document.querySelectorAll(".project-filter li");
    const allItems = document.querySelectorAll(".filter-item");
    const loadMoreBtn = document.querySelector(".project-style2-load-more-button a");

    let visibleCount = 3;
    const loadCount = 3;

    // Initial Setup: store full list
    let currentFilter = 'all';

    // Filter click logic
    filterButtons.forEach(button => {
        button.addEventListener("click", function () {
            filterButtons.forEach(btn => btn.classList.remove("active"));
            this.classList.add("active");
            currentFilter = this.getAttribute("data-filter");

            let shown = 0;
            allItems.forEach(item => {
                const matches = currentFilter === 'all' || item.classList.contains(currentFilter);
                if (matches && shown < visibleCount) {
                    item.style.display = 'block';
                    shown++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Show Load More button if there are hidden items
            const hiddenItems = Array.from(allItems).filter(item =>
                (currentFilter === 'all' || item.classList.contains(currentFilter)) &&
                item.style.display === 'none'
            );
            loadMoreBtn.style.display = hiddenItems.length > 0 ? 'inline-block' : 'none';
        });
    });

    // Load More logic
    loadMoreBtn.addEventListener("click", function (e) {
        e.preventDefault();
        let shown = 0;
        allItems.forEach(item => {
            const matches = currentFilter === 'all' || item.classList.contains(currentFilter);
            if (matches && item.style.display === 'none' && shown < loadCount) {
                item.style.display = 'block';
                shown++;
            }
        });

        const hiddenItems = Array.from(allItems).filter(item =>
            (currentFilter === 'all' || item.classList.contains(currentFilter)) &&
            item.style.display === 'none'
        );
        if (hiddenItems.length === 0) {
            loadMoreBtn.style.display = 'none';
        }
    });

    // Trigger default filter (All)
    filterButtons[0].click();
});
</script>


@endsection
