<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class AboutSection extends Model
{
    use Sluggable;
    protected $fillable = [
        'tagline_en',
        'tagline_am',
        'title_en',
        'title_am',
        'subtitle_en',
        'subtitle_am',
        'content_en',
        'content_am',
        'featured_image',
        'featured_image_caption_en',
        'featured_image_caption_am',
        'certificate_image',
        'certificate_image_caption_en',
        'certificate_image_caption_am',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
