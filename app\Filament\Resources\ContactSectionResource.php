<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContactSectionResource\Pages;
use App\Filament\Resources\ContactSectionResource\RelationManagers;
use App\Models\ContactSection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContactSectionResource extends Resource
{
    protected static ?string $model = ContactSection::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')->label('Tag (English)')->required(),
                Forms\Components\TextInput::make('tag_am')->label('Tag (Amharic)')->required(),
                Forms\Components\RichEditor::make('title_en')->label('Title (English)')->required(),
                Forms\Components\RichEditor::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\RichEditor::make('tip_en')->label('Tip (English)')->required(),
                Forms\Components\RichEditor::make('tip_am')->label('Tip (Amharic)')->required(),
                Forms\Components\TextInput::make('contact_tip_en')->label('Contact Tip (English)')->required(),
                Forms\Components\TextInput::make('contact_tip_am')->label('Contact Tip (Amharic)')->required(),
                Forms\Components\TextInput::make('contact')->label('Contact (Phone)')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('contact')->label('Contact')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('tip_en')->label('Tip (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('tip_am')->label('Tip (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('contact_tip_en')->label('Contact Tip (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('contact_tip_am')->label('Contact Tip (Amharic)')->sortable()->searchable()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContactSections::route('/'),
            'create' => Pages\CreateContactSection::route('/create'),
            'edit' => Pages\EditContactSection::route('/{record}/edit'),
        ];
    }
}
