<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class ContactSection extends Model
{
    use Sluggable;
    protected $fillable = [
        'tag_en',
        'tag_am',
        'title_en',
        'title_am',
        'contact_tip_en',
        'contact_tip_am',
        'contact',
        'tip_en',
        'tip_am',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
