<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FeatureResource\Pages;
use App\Filament\Resources\FeatureResource\RelationManagers;
use App\Models\Feature;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FeatureResource extends Resource
{
    protected static ?string $model = Feature::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('title_en')->label('Title (English)')->required(),
                Forms\Components\RichEditor::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\Textarea::make('content_en')->label('Content (English)')->required(),
                Forms\Components\Textarea::make('content_am')->label('Content (Amharic)')->required(),
                Forms\Components\FileUpload::make('bg_image')->label('Background Image')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_en')->label('Content (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_am')->label('Content (Amharic)')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('bg_image')->label('Background Image')

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFeatures::route('/'),
            'create' => Pages\CreateFeature::route('/create'),
            'edit' => Pages\EditFeature::route('/{record}/edit'),
        ];
    }
}
