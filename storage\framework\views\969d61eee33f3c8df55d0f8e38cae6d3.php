<?php $__env->startSection('title','Home'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Display Error Messages -->
    <?php if(session('error')): ?>
    <div class="search-message-bar error-message" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; padding: 15px 0; text-align: center; position: relative; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 1000;">
        <div class="container">
            <div class="message-content" style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                <span class="icon-close" style="font-size: 18px;"></span>
                <strong style="font-size: 16px; font-weight: 600;"><?php echo e(session('error')); ?></strong>
                <button type="button" class="close-btn" style="position: absolute; right: 20px; background: none; border: none; color: white; font-size: 24px; cursor: pointer; opacity: 0.8; transition: opacity 0.3s;" onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.8'">&times;</button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Display Success Messages -->
    <?php if(session('success')): ?>
    <div class="search-message-bar success-message" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px 0; text-align: center; position: relative; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 1000;">
        <div class="container">
            <div class="message-content" style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                <span class="icon-check" style="font-size: 18px;"></span>
                <strong style="font-size: 16px; font-weight: 600;"><?php echo e(session('success')); ?></strong>
                <button type="button" class="close-btn" style="position: absolute; right: 20px; background: none; border: none; color: white; font-size: 24px; cursor: pointer; opacity: 0.8; transition: opacity 0.3s;" onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.8'">&times;</button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Start Main Slider -->
<section class="main-slider style1">
    <div class="slider-box">
        <!-- Banner Carousel -->
        <div class="banner-carousel owl-theme owl-carousel">
            <!-- Slide -->
            <?php $__currentLoopData = $slides; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(!empty($slide->{'title_'. app()->getLocale()})): ?>
            <div class="slide">
                <div class="image-layer" style="background-image:url(<?php echo e(asset('storage/'. $slide->layer_image)); ?>)"></div>
                <div class="slider-bg-box" style="background-image: url(<?php echo e(asset('storage/'. $slide->bg_image)); ?>);"></div>
                <div class="slider-image"><img class="float-bob-y" src="<?php echo e(asset('storage/'. $slide->featured_image)); ?>" alt=""></div>
                <div class="round-box">
                    
                    <?php echo $slide->{'tag_'. app()->getLocale()}; ?>

                </div>

                <div class="auto-container">
                    <div class="content">
                        <div class="big-title">
                            <h2><?php echo $slide->{'title_'. app()->getLocale()}; ?></h2>
                        </div>
                        <div class="text">
                            <p><?php echo $slide->{'content_'. app()->getLocale()}; ?></p>
                        </div>
                        <div class="btns-box">

                            <div class="slider-video-gallery">
                                <div class="icon">
                                    <a class="video-popup" title="Video Gallery" href="https://www.youtube.com/watch?v=oxsHnllk9rE">
                                        <span class="icon-play playicon"></span>
                                    </a>
                                </div>
                                <div class="title">
                                    <h6>Watch Video</h6>
                                    <p>Our Source, Our Pride</p>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <!-- Slide -->
            
        </div>
    </div>
</section>
<!-- End Main Slider -->

<section class="choose-style2-area">
    <div class="auto-container">
        <div class="outer-box">
            <div class="row">
                <!--Start Single Choose Box Style2-->
                <?php
                $iconClasses = ['icon-water-drop-1', 'icon-write-message', 'icon-truck', 'icon-hand'];
                ?>
                <?php $__currentLoopData = $datas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i=> $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(!empty($data->{'title_'. app()->getLocale()})): ?>
                <div class="col-xl-3 step1">
                    <div class="single-choose-box-style2">
                        <div class="inner">
                            <div class="outer-icon">
                                <div class="icon-bg" style="background-image: url('<?php echo e(asset('storage/'. $data->icon1)); ?>');"></div>
                                <span class="<?php echo e($iconClasses[$i % count($iconClasses)]); ?>"></span>
                            </div>
                            <div class="inner-content">
                                <div class="inner-icon">
                                    <div class="icon-bg" style="background-image: url('<?php echo e(asset('storage/'. $data->icon2)); ?>');"></div>
                                    <span class="<?php echo e($iconClasses[$i % count($iconClasses)]); ?>"></span>
                                </div>
                                <div class="title">
                                    <h3><?php echo e($data-> {'title_'. app()->getLocale()}); ?></h3>
                                    <p><?php echo e($data-> {'content_'. app()->getLocale()}); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <!--End Single Choose Box Style2-->
                <!--Start Single Choose Box Style2-->
                
                <!--End Single Choose Box Style2-->
            </div>

        </div>
    </div>
</section>


<section class="about-style2-area" id="about-us-section">
    <div class="about-style2_image-bg" style="background-image: url(assets/images/resources/about-style2-bg.jpg);">
        <div class="about-style2-image-box wow slideInLeft animated" data-wow-delay="100ms" data-wow-duration="2500ms" style="visibility: visible; animation-duration: 2500ms; animation-delay: 100ms; animation-name: slideInLeft;">
            <?php if(!empty($about_section->featured_image)): ?>
            <img src="<?php echo e(asset('storage/'. $about_section->featured_image)); ?>" alt="">
            <?php endif; ?>
            <div class="thm-round-box1 js-tilt paroller" style="transform: unset; transition: transform 0.6s cubic-bezier(0, 0, 0, 1); will-change: transform;">
                <?php if(!empty($about_section->{ 'featured_image_caption_'. app()->getLocale()})): ?>
                <h3><?php echo $about_section->{ 'featured_image_caption_'. app()->getLocale()}; ?></h3>
                <?php endif; ?>
            <div class="js-tilt-glare" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; overflow: hidden;"><div class="js-tilt-glare-inner" style="position: absolute; top: 50%; left: 50%; pointer-events: none; background-image: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%); width: 320px; height: 320px; transform: rotate(180deg) translate(-50%, -50%); transform-origin: 0% 0% 0px; opacity: 0;"></div></div></div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-xl-6">

            </div>
            <div class="col-xl-6">
                <div class="about-style2_content">
                    <?php if(!empty($about_section->{ 'tag_'. app()->getLocale() } )): ?>
                    <div class="sec-title">
                        <div class="sub-title">
                            <h5><?php echo e($about_section->{ 'tag_'. app()->getLocale() }); ?></h5>
                        </div>
                        <h2><?php echo $about_section->{'title_'. app()->getLocale()}; ?></h2>
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php if(!empty($about_section->{'subtitle_'. app()->getLocale()} )): ?>
                    <div class="inner-content">
                        <h5><?php echo $about_section->{'subtitle_'. app()->getLocale()}; ?></h5>
                        <div class="text">
                            <p><?php echo $about_section->{'content_'. app()->getLocale()}; ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="bottom-box">
                        <?php if(!empty($about_section->{'certificate_image_caption_'. app()->getLocale()} )): ?>
                        <div class="certification-box">
                            <div class="certification-box-bg" style="background-image: url(assets/images/shape/certification-box-bg.jpg);"></div>
                            <div class="inner">
                                <img src="<?php echo e(asset('storage/' .$about_section->certificate_image)); ?>" alt="">
                                <h3><?php echo $about_section->{'certificate_image_caption_'. app()->getLocale()}; ?></h3>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="highlights-box" id="about-list">
                            <ul>
                                <?php $__currentLoopData = $about_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $about): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(!empty($about->{'title_'. app()->getLocale()})): ?>
                                <li class="wow fadeInRight animated" data-wow-delay="200ms" data-wow-duration="1500ms" style="visibility: visible; animation-duration: 1500ms; animation-delay: 200ms; animation-name: fadeInRight;">
                                    <div class="icon">
                                        <div class="icon-bg" style="background-image: url('<?php echo e(asset('storage/'. $about->icon)); ?>');"></div>
                                        <span class="icon-shield"></span>
                                    </div>
                                    <div class="text">
                                        <h3><?php echo e($about->{'title_'. app()->getLocale()}); ?></h3>
                                        <p><?php echo e($about->{'content_'. app()->getLocale()}); ?></p>
                                    </div>
                                </li>
                                <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                

                            </ul>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</section>



<!--Start Fact Counter Area-->
<section class="fact-counter-area" id="fact">
    <div class="auto-container">
        <div class="row">
            <div class="col-xl-12">
                <div class="fact-counter_box">
                    <ul class="clearfix">
                        <?php $__currentLoopData = $facts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $fact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if(!empty($fact->{'fact_'. app()->getLocale()})): ?>

                        <li class="single-fact-counter wow slideInUp" data-wow-delay="00ms" data-wow-duration="1500ms">
                            <div class="border-box"><img src="assets/images/shape/fact-counter-border.png" alt=""/></div>
                            <div class="outer-box">
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="<?php echo e($fact->estimate); ?>">0</span>
                                    <?php if($i == 0): ?>
                                    <span class="k">k</span>
                                    <?php elseif($i == 1): ?>
                                    <span class="plus">+</span>
                                    <?php endif; ?>
                                </div>
                                <div class="title">
                                    <h6><?php echo $fact->{'fact_'. app()->getLocale()}; ?></h6>
                                </div>
                            </div>
                        </li>
                        <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        

                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Fact Counter Area-->

<!--Start About Style1 Area-->
<section class="about-style1-area" id="about-company">
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="about-style1_top">
                    <div class="sec-title">
                        <div class="sub-title">
                            <?php if(!empty($about_company->{'tag_'. app()->getLocale()})): ?>
                            <h5><?php echo e($about_company->{'tag_'. app()->getLocale()}); ?></h5>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty($about_company->{'title_'. app()->getLocale()})): ?>
                        <h2><?php echo $about_company->{'title_'. app()->getLocale()}; ?></h2>
                        <?php endif; ?>
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    <div class="our-certification-box">
                        <div class="certificate-logo">
                            <?php if(!empty($about_company->certificate_image)): ?>
                            <img src="<?php echo e('storage/'. $about_company->certificate_image); ?>" alt="">
                            <?php endif; ?>
                        </div>
                        <div class="text">
                            <?php if(!empty($about_company->{'certificate_title_'. app()->getLocale()})): ?>
                            <h3><span><?php echo $about_company->{'certificate_title_'. app()->getLocale()}; ?></h3>
                            <p><?php echo $about_company->{'certificate_content_'. app()->getLocale()}; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" id="company-goal">
            <div class="col-xl-12">
                <div class="about-style1_content">
                    <div class="about-style1_tab tabs-box">
                        <ul class="tab-buttons clearfix">
                            <li data-tab="#about" class="tab-btn active-btn">
                                <div class="left">
                                    <h2>01.</h2>
                                </div>
                                <div class="right">
                                    <?php if(!empty($company_story->{'tag_'.app()->getLocale() })): ?>
                                    <h5><?php echo $company_story->{'tag_'.app()->getLocale() }; ?></h5>
                                    <?php endif; ?>
                                </div>
                            </li>
                            <li data-tab="#goal" class="tab-btn">
                                <div class="left">
                                    <h2>02.</h2>
                                </div>
                                <div class="right">
                                    <?php if(!empty($company_goal->{'tag_'. app()->getLocale()})): ?>
                                    <h5><?php echo $company_goal->{'tag_'. app()->getLocale()}; ?></h5>
                                    <?php endif; ?>
                                </div>
                            </li>
                        </ul>

                        <div class="tabs-content">
                            <div class="pattern-bg" style="background-image: url(assets/images/pattern/thm-pattern-1.png);"></div>
                            <!--Tab-->
                            <div class="tab active-tab" id="about">
                                <diav class="about-style1-tab-content clearfix">
                                    <?php if(!empty($company_story->featured_image)): ?>
                                    <div class="about-style1-tab-content_bg" style="background-image: url('<?php echo e('storage/'.$company_story->featured_image); ?>');"></div>
                                    <?php endif; ?>
                                    <div class="inner-content">
                                        <div class="sec-title">
                                            <?php if(!empty($company_story->{'title_'. app()->getLocale()} )): ?>
                                            <h2><?php echo $company_story->{'title_'. app()->getLocale()}; ?></h2>
                                            <?php endif; ?>
                                            <div class="decor">
                                                <img src="assets/images/shape/decor.png" alt="">
                                            </div>
                                        </div>
                                        <?php if(!empty($company_story->{'content_'. app()->getLocale()} )): ?>
                                        <p><?php echo $company_story->{'content_'. app()->getLocale()}; ?></p>
                                        <?php endif; ?>
                                        <div class="btn-box">
                                            <a class="btn-two" href="/about-us"><span class="icon-right-arrow"></span>Read More</a>
                                        </div>
                                    </div>
                                </diav>
                            </div>
                            <!--Tab-->
                            <!--Tab-->
                            <div class="tab" id="goal">
                                <div class="about-style1-tab-content clearfix">
                                    <?php if(!empty($company_goal->featured_image)): ?>
                                    <div class="about-style1-tab-content_bg" style="background-image: url('<?php echo e('storage/'. $company_goal->featured_image); ?>');"></div>
                                    <?php endif; ?>
                                    <div class="inner-content">
                                        <div class="sec-title">
                                            <?php if(!empty($company_goal->{'title_'. app()->getLocale()} )): ?>
                                            <h2><?php echo $company_goal->{'title_'. app()->getLocale()}; ?></h2>
                                            <?php endif; ?>
                                            <div class="decor">
                                                <img src="assets/images/shape/decor.png" alt="">
                                            </div>
                                        </div>
                                        <?php if(!empty($company_goal->{'content_'. app()->getLocale()} )): ?>
                                        <p><?php echo $company_goal->{'content_'. app()->getLocale()}; ?></p><br>
                                        <div id="goal-detail-content" class="goal-accordion-content" style="max-height: 0 !important; overflow: hidden !important; opacity: 0 !important; padding: 0 !important;">
                                                    <?php echo $company_goal->{'detail_content_'. app()->getLocale()}; ?>

                                        </div>
                                        <?php endif; ?>
                                        <div class="btns-box">
                                            <a class="btn-two accordion-btn" href="#" id="goal-read-more-btn" onclick="toggleGoalContent(); return false;">
                                                <span class="icon-right-arrow"></span>
                                                <span id="goal-btn-text">Read More</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--Tab-->
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

<!--End About Style1 Area-->

<!--Start Shop Style1 Area-->
<section class="shop-style1-area" id="shop">
    <div class="shop-top-image-box">
        <div class="inner">
            <?php if(!empty($shope->feature_image1)): ?>
            <img src="<?php echo e('storage/'. $shope->feature_image1); ?>" alt="">
            <?php endif; ?>
        </div>
        <div class="ice paroller">
            <?php if(!empty($shope->feature_image2)): ?>
            <img class="zoom-fade" src="<?php echo e('storage/'. $shope->feature_image2); ?>" alt="">
            <?php endif; ?>
        </div>
        <div class="round-box paroller-2">
            <?php if(!empty($shope->{'order_caption_'. app()->getLocale()} )): ?>
            <h3><?php echo $shope->{'order_caption_'. app()->getLocale()}; ?></h3>
            <?php endif; ?>
        </div>
    </div>
    <?php if(!empty($shope->{'water_mark_'. app()->getLocale()} )): ?>
    <div class="big-title"><?php echo e($shope->{'water_mark_'. app()->getLocale()}); ?></div>
    <?php endif; ?>
    <div class="container" id="products">
        <div class="sec-title text-center">
            <div class="sub-title">
                <?php if(!empty($product_intro->{'tag_'.app()->getLocale()} )): ?>
                <h5><?php echo e($product_intro->{'tag_'.app()->getLocale()}); ?></h5>
                <?php endif; ?>
            </div>
            <?php if(!empty($product_intro->{'title_'. app()->getLocale()} )): ?>
            <h2><?php echo $product_intro->{'title_'. app()->getLocale()}; ?></h2>
            <?php endif; ?>
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="theme_carousel shop-carousel_1 owl-dot-style1 owl-theme owl-carousel owl-loaded owl-drag" data-options="{&quot;loop&quot;: true, &quot;margin&quot;: 30, &quot;autoheight&quot;:true, &quot;lazyload&quot;:true, &quot;nav&quot;: false, &quot;dots&quot;: true, &quot;autoplay&quot;: true, &quot;autoplayTimeout&quot;: 6000, &quot;smartSpeed&quot;: 300, &quot;responsive&quot;:{ &quot;0&quot; :{ &quot;items&quot;: &quot;1&quot; }, &quot;600&quot; :{ &quot;items&quot; : &quot;1&quot; }, &quot;768&quot; :{ &quot;items&quot; : &quot;1&quot; } , &quot;992&quot;:{ &quot;items&quot; : &quot;2&quot; }, &quot;1200&quot;:{ &quot;items&quot; : &quot;3&quot; }}}">
                    <div class="owl-stage-outer">
                        <div class="owl-stage" style="transform: translate3d(-2400px, 0px, 0px); transition: 0.3s; width: 7600px;">
                            <?php $__currentLoopData = $product_home; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(!empty($product->{'name_'. app()->getLocale()})): ?>
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="single-shop-item single-shop-item--style2">
                                    <div class="single-shop-item_inner">
                                        <div class="img-holder">
                                            <img src="<?php echo e(asset('storage/'. $product->featured_image)); ?>" alt="">
                                            <div class="overlay">
                                                <span class="icon-email"></span>
                                                <a href="#"><?php echo e($product->{'tip_'.app()->getLocale()}); ?></a>
                                            </div>
                                        </div>

                                        <div class="title-holder">
                                            <h3><a href="<?php echo e(route('product.detail', $product->slug)); ?>"><?php echo e($product->{'name_'. app()->getLocale()}); ?></a></h3>

                                            <div class="btn-box">
                                                <a class="btn-one" href="<?php echo e(route('product.detail', $product->slug)); ?>">
                                                    <div class="round"></div>
                                                    <span class="txt">Read More</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                            

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Shop Style1 Area-->

<!--Start Choose Style1 Area-->
<section class="choose-style1-area" id="choose-section">
    <div class="container">
        <div class="row">
            <div class="col-xl-4">
                <div class="choose-style1_image-box wow slideInLeft" data-wow-delay="100ms" data-wow-duration="2500ms">
                    <?php if(!empty($why_intro->featured_image)): ?>
                    <img src="<?php echo e('storage/'. $why_intro->featured_image); ?>" alt=""/>
                    <?php endif; ?>
                    <div class="round-box js-tilt paroller">
                        <?php if(!empty($why_intro->{'featured_image_caption_'. app()->getLocale()})): ?>
                        <h3><?php echo $why_intro->{'featured_image_caption_'. app()->getLocale()}; ?></h3>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-xl-8">
                <div class="choose-style1-content">
                    <div class="sec-title">
                        <div class="sub-title">
                            <?php if(!empty($why_intro->{'tag_'. app()->getLocale()} )): ?>
                            <h5><?php echo e($why_intro->{'tag_'. app()->getLocale()}); ?></h5>
                            <?php endif; ?>
                        </div>
                        <?php if(!empty( $why_intro->{'title_'. app()->getLocale()} )): ?>
                        <h2><?php echo $why_intro->{'title_'. app()->getLocale()}; ?></h2>
                        <?php endif; ?>
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    <div class="inner-content">
                        <div class="shape">
                            <img src="assets/images/shape/choose-style1-shape-1.png" alt="">
                        </div>
                        <ul class="clearfix">
                            <?php $__currentLoopData = $lists-> take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i=> $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(!empty($list->{'title_'.app()->getLocale()})): ?>
                            <li class="wow fadeInLeft" data-wow-delay="100ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    <?php if($i == 0): ?>
                                    <span class="icon-water-drop-1"></span>
                                    <?php elseif($i == 1): ?>
                                    <span class="icon-write-message"></span>
                                    <?php endif; ?>
                                </div>
                                <div class="text">
                                    <h3><?php echo e($list->{'title_'.app()->getLocale()}); ?></h3>
                                    <p><?php echo e($list->{'content_'.app()->getLocale()}); ?></p>
                                </div>
                            </li>
                            <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                        </ul>
                        <ul class="clearfix">
                            <?php $__currentLoopData = $lists-> skip(2)->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i=> $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(!empty($list->{'title_'.app()->getLocale()})): ?>
                            <li class="wow fadeInLeft" data-wow-delay="300ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    <?php if($i == 2): ?>
                                    <span class="icon-shield"></span>
                                    <?php elseif($i == 3): ?>
                                    <span class="icon-medal"></span>
                                    <?php endif; ?>
                                </div>
                                <div class="text">
                                    <h3><?php echo e($list->{'title_'.app()->getLocale()}); ?></h3>
                                    <p><?php echo e($list->{'content_'.app()->getLocale()}); ?></p>
                                </div>
                            </li>
                            <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                        </ul>
                        <ul class="clearfix">
                            <?php $__currentLoopData = $lists-> skip(4)->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i=> $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(!empty($list->{'title_'.app()->getLocale()})): ?>
                            <li class="wow fadeInLeft" data-wow-delay="500ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    <?php if($i == 4): ?>
                                    <span class="icon-hand"></span>
                                    <?php elseif($i == 5): ?>
                                    <span class="icon-truck"></span>
                                    <?php endif; ?>
                                </div>
                                <div class="text">
                                    <h3><?php echo e($list->{'title_'.app()->getLocale()}); ?></h3>
                                    <p><?php echo e($list->{'content_'.app()->getLocale()}); ?></p>
                                </div>
                            </li>
                            <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!--End Choose Style1 Area-->

<!--Start Features Style1 Area-->
<section class="features-style1-area" id="features">
    <div class="auto-container">
        <div class="row">
            <div class="col-xl-6">
                <div class="features-style1_one-content">
                    <?php if(!empty($feature_intro->bg_image)): ?>
                    <div class="features-style1_one-content-bg" style="background-image: url('<?php echo e(asset('storage/' .$feature_intro->bg_image)); ?>');"></div>
                    <?php endif; ?>
                    <div class="inner-content">
                        <div class="sec-title">
                            <div class="sub-title">
                                <?php if(!empty($feature_intro->{'tag_'. app()->getLocale()})): ?>
                                <h5><?php echo e($feature_intro->{'tag_'. app()->getLocale()}); ?></h5>
                                <?php endif; ?>
                            </div>
                            <?php if(!empty($feature_intro->{'title_'. app()->getLocale()})): ?>
                            <h2><?php echo $feature_intro->{'title_'. app()->getLocale()}; ?></h2>
                            <?php endif; ?>
                            <div class="decor">
                                <img src="assets/images/shape/decor.png" alt="">
                            </div>
                        </div>
                        <div class="text">
                            <?php if(!empty($feature_intro->{'intro_'. app()->getLocale()} )): ?>
                            <p><?php echo e($feature_intro->{'intro_'. app()->getLocale()}); ?></p>
                            <?php endif; ?>
                            <ul>
                                <?php $__currentLoopData = $feature_lists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(!empty($list->{'content_'. app()->getLocale()})): ?>
                                <li><span class="icon-water-drop"></span><?php echo e($list->{'content_'. app()->getLocale()}); ?></li>
                                
                                <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(!empty($feature->{'title_'.app()->getLocale()} )): ?>
            <div class="col-xl-3">
                <div class="features-style1_single-box">
                    <div class="features-style1_single-box-bg" style="background-image: url(<?php echo e(asset('storage/' .$feature->bg_image)); ?>);"></div>
                    <div class="inner-content">
                        <h2><?php echo $feature->{'title_'.app()->getLocale()}; ?></h2>
                        <p><?php echo e($feature->{'content_'.app()->getLocale()}); ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            

        </div>
    </div>
</section>

<!--End Features Style1 Area-->


<!--Start Working process area -->
<section class="working-process-area">
    <div class="working-process-area-bg" style="background-image: url(assets/images/parallax-background/working-process-area-bg.jpg);"></div>
    <div class="container">
        <div class="sec-title text-center">
            <div class="sub-title">
                <?php if(!empty($process_intro->{'tag_'. app()->getLocale()})): ?>
                <h5><?php echo e($process_intro->{'tag_'. app()->getLocale()}); ?></h5>
                <?php endif; ?>
            </div>
            <?php if(!empty($process_intro->{'title_'. app()->getLocale()})): ?>
            <h2 class="clr_white"><?php echo $process_intro->{'title_'. app()->getLocale()}; ?></h2>
            <?php endif; ?>
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <!--Start Working process Single-->
            <?php $__currentLoopData = $process_cards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i=> $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(!empty($card->{'title_'. app()->getLocale()})): ?>
            <div class="col-xl-4">
                <div class="single-working-process wow fadeInUp" data-wow-delay="00ms" data-wow-duration="1500ms">
                    <div class="counting-box clearfix">
                        <div class="text">
                            <h6>Step</h6>
                        </div>
                        <div class="count"></div>
                    </div>
                    <div class="content">
                        <h3><?php echo e($card->{'title_'. app()->getLocale()}); ?></h3>
                        <p><?php echo e($card->{'content_'. app()->getLocale()}); ?></p>
                    </div>
                    <div class="icon">
                        <?php if($i == 0): ?>
                        <span class="icon-order"></span>
                        <?php elseif($i == 1): ?>
                        <span class="icon-package"></span>
                        <?php elseif($i == 2): ?>
                        <span class="icon-truck-1"></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <!--End Working process Single-->
            <!--Start Working process Single-->
            
            <!--End Working process Single-->
        </div>
    </div>
</section>


<!--End Working process area -->

<!--Start Contact Style1 Area-->
<section class="contact-style1-area" id="contact-us">
    <div class="contact-form-box1_bg" style="background-image: url(assets/images/resources/contact-form-box1_bg.jpg);"></div>
    <div class="thm-round-box1">
        <h3>Top<br> Customer<br> Support</h3>
    </div>
    <div class="gray-bg"></div>
    <div class="container">
        <div class="row">

            <div class="col-xl-6">
                <div class="contact-style1-content">
                    <div class="shape1" data-aos="fade-right" data-aos-easing="linear" data-aos-duration="2000">
                        <img class="paroller-2" src="assets/images/shape/thm-shape-2.png" alt="">
                    </div>
                    <div class="sec-title">
                        <div class="sub-title">
                            <?php if(!empty($contact_section->{'tag_'.app()->getLocale()})): ?>
                            <h5><?php echo e($contact_section->{'tag_'.app()->getLocale()}); ?></h5>
                        </div>
                        <h2><?php echo $contact_section->{'title_'. app()->getLocale()}; ?></h2>
                        <?php endif; ?>
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    <div class="inner-content">
                        <div class="quick-contact-box">
                            <div class="icon">
                                <span class="icon-calling"></span>
                            </div>
                            <div class="title">
                                <?php if(!empty($contact_section->{'contact_tip_'. app()->getLocale()} )): ?>
                                <h3><?php echo e($contact_section->{'contact_tip_'. app()->getLocale()}); ?></h3>
                                <h2><a href="tel:+50033333"><?php echo e($contact_section->contact); ?></a></h2>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="text">
                            <?php if(!empty($contact_section->{'tip_'.app()->getLocale()} )): ?>
                            <p><?php echo $contact_section->{'tip_'.app()->getLocale()}; ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="btn-box">
                            <a class="btn-one" href="#">
                                <div class="round"></div>
                                <span class="txt">Call Back</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-6" id="enquire">
                <div class="contact-form-box1">
                    <div class="top-title">
                        <h2>Enquire With Our Team</h2>
                    </div>
                    <form id="contact-form" name="contact_form" class="default-form1" action="#" method="post">
                        <div class="input-box">
                            <input type="text" name="form_name" value="" placeholder="Your Name" required="">
                            <div class="icon">
                                <i class="fa fa-user" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <input type="email" name="form_email" value="" placeholder="Email Address" required="">
                            <div class="icon">
                                <i class="fa fa-envelope" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <div class="select-box">
                                <div class="round-shape"></div>
                                <select class="wide">
                                   <option data-display="Service You Need">Service You Need</option>
                                   <option value="1">Bottled Water</option>
                                   <option value="2">Water Dispenser</option>
                                   <option value="3">Water Trailers</option>
                                </select>
                            </div>
                            <div class="icon">
                                <i class="fa fa-cog" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <input type="text" name="form_address" value="" placeholder="Your Address">
                            <div class="icon">
                                <i class="fa fa-map-marker" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="button-box">
                            <div class="left">
                                <div class="checked-box1">
                                    <input type="checkbox" name="skipper1" id="skipper" checked>
                                    <label for="skipper"><span></span>I agree to receive updates<br> from Aqua Uno</label>
                                </div>
                            </div>
                            <div class="right">
                                <button class="btn-one" type="submit" data-loading-text="Please wait...">
                                    <span class="round"></span>
                                    <span class="txt">Continue</span>
                                </button>
                            </div>
                        </div>
                    </form>

                </div>
            </div>

        </div>
    </div>
</section>
<!--End Contact Style1 Area-->


<!--Start Testimonials Style1 area -->
<section class="testimonials-style1-area" id="testimonial">
    <div class="container">

        <div class="sec-title text-center">
            <?php if(!empty($testimonial_intro->{'tag_'. app()->getLocale()})): ?>
            <div class="sub-title">
                <h5><?php echo e($testimonial_intro->{'tag_'. app()->getLocale()}); ?></h5>
            </div>
            <h2><?php echo $testimonial_intro->{'title_'.app()->getLocale()}; ?></h2>
            <?php endif; ?>
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="theme_carousel testimonials-carousel_1 owl-dot-style1 owl-theme owl-carousel" data-options='{"loop": true, "margin": 30, "autoheight":true, "lazyload":true, "nav": false, "dots": true, "autoplay": true, "autoplayTimeout": 6000, "smartSpeed": 300, "responsive":{ "0" :{ "items": "1" }, "600" :{ "items" : "1" }, "768" :{ "items" : "1" } , "992":{ "items" : "1" }, "1200":{ "items" : "1" }}}'>
                    <!--Start Single Testimonials Style1-->
                    <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(!empty($testimonial->{'title_'. app()->getLocale()} )): ?>
                    <div class="single-testimonials-style1">
                        <div class="img-box">
                            <img src="<?php echo e(asset('storage/'. $testimonial->profile)); ?>" alt=""/>
                            <div class="round-1"></div>
                            <div class="round-2"></div>
                        </div>
                        <div class="inner-content">
                            <div class="content-box">
                                <div class="rateing-box">
                                    <ul>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                    </ul>
                                </div>
                                <h3><?php echo e($testimonial->{'title_'. app()->getLocale()}); ?></h3>
                                <p><?php echo e($testimonial->{'content_'. app()->getLocale()}); ?></p>
                                <h4><?php echo e($testimonial->{'name_'. app()->getLocale()}); ?>  <span><?php echo e($testimonial->{'address_'.app()->getLocale()}); ?></span></h4>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <!--End Single Testimonials Style1-->
                    <!--Start Single Testimonials Style1-->
                    
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Testimonials Style1 area -->




<section class="partner-area" id="partners">
    <div class="partner-bg" style="background-image: url(assets/images/parallax-background/partner-bg.jpg);"></div>
    <div class="container">
        <div class="partner-slider">
            <ul class="partner-box">
                <!--Start Single Partner Logo Box-->
                <?php $__currentLoopData = $partners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(!empty($partner->logo)): ?>
                <li class="single-partner-logo-box">
                    <a href="https://gulfingot.com/"><img src="<?php echo e(asset('storage/'. $partner->logo)); ?>" alt="Awesome Image"></a>
                </li>
                <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <!--End Single Partner Logo Box-->
                
            </ul>
        </div>
    </div>
</section>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.search-message-bar {
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.search-message-bar .close-btn:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .search-message-bar .message-content {
        flex-direction: column;
        gap: 5px;
    }

    .search-message-bar .close-btn {
        position: static;
        margin-top: 5px;
    }
}
</style>
<style>
.goal-accordion-content {
    max-height: 0 !important;
    overflow: hidden !important;
    transition: max-height 0.5s ease-in-out, padding 0.3s ease, opacity 0.3s ease !important;
    padding: 0 !important;
    opacity: 0 !important;
}

.goal-accordion-content.active {
    max-height: 1000px !important; /* Increased for longer content */
    padding: 15px 0 !important;
    opacity: 1 !important;
}

.accordion-btn {
    transition: all 0.3s ease;
}

.accordion-btn:hover {
    transform: translateY(-2px);
}

/* Rotate span 90 degrees on hover */
.btns-box:hover span,
.btn-box:hover span {
    transform: rotate(90deg);
    transition: transform 0.3s ease;
}

.btns-box span,
.btn-box span {
    display: inline-block;
    transition: transform 0.3s ease;
}

</style>

<script>
function toggleGoalContent() {
    const content = document.getElementById('goal-detail-content');
    const btnText = document.getElementById('goal-btn-text');

    if (!content || !btnText) {
        return;
    }

    if (content.classList.contains('active')) {
        content.classList.remove('active');
        // Force hide with important
        content.style.setProperty('max-height', '0', 'important');
        content.style.setProperty('opacity', '0', 'important');
        content.style.setProperty('padding', '0', 'important');
        btnText.textContent = 'Read More';
    } else {
        content.classList.add('active');
        // Force show with important
        content.style.setProperty('max-height', '1000px', 'important');
        content.style.setProperty('opacity', '1', 'important');
        content.style.setProperty('padding', '15px 0', 'important');
        btnText.textContent = 'Read Less';
    }
}
</script>

<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Auto-hide message bars after 5 seconds
setTimeout(function() {
    const messageBars = document.querySelectorAll('.search-message-bar');
    messageBars.forEach(function(messageBar) {
        messageBar.style.transition = 'transform 0.5s ease-out, opacity 0.5s ease-out';
        messageBar.style.transform = 'translateY(-100%)';
        messageBar.style.opacity = '0';
        setTimeout(function() {
            messageBar.remove();
        }, 500);
    });
}, 5000);

// Handle close button clicks
document.addEventListener('DOMContentLoaded', function() {
    const closeButtons = document.querySelectorAll('.close-btn');
    closeButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const messageBar = this.closest('.search-message-bar');
            messageBar.style.transition = 'transform 0.5s ease-out, opacity 0.5s ease-out';
            messageBar.style.transform = 'translateY(-100%)';
            messageBar.style.opacity = '0';
            setTimeout(function() {
                messageBar.remove();
            }, 500);
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.frontend', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\development\aqua-uno\resources\views/welcome.blade.php ENDPATH**/ ?>