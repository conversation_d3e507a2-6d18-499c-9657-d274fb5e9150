<?php

namespace App\Filament\Resources\ProcessCardResource\Pages;

use App\Filament\Resources\ProcessCardResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListProcessCards extends ListRecords
{
    protected static string $resource = ProcessCardResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
