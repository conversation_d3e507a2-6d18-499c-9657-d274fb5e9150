



/*==============================================
    Main Header Css        
===============================================*/
.main-header {
	position: absolute;
	left: 0px;
	top: 0px;
	width: 100%;
    margin: 0px;
	z-index: 999;
	transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-webkit-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
}
.sticky-header {
	position: fixed;
	top: 0px;
	left: 0px;
	width: 100%;
	padding: 0px 0px;
    background: rgba(255, 255, 255, 0.98);
	opacity: 0;
    visibility: hidden;
	transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-webkit-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	z-index: 0;
}
.fixed-header .sticky-header {
	visibility: visible;
	-ms-animation-name: fadeInDown;
	-moz-animation-name: fadeInDown;
	-op-animation-name: fadeInDown;
	-webkit-animation-name: fadeInDown;
	animation-name: fadeInDown;
	-ms-animation-duration: 500ms;
	-moz-animation-duration: 500ms;
	-op-animation-duration: 500ms;
	-webkit-animation-duration: 500ms;
	animation-duration: 500ms;
	-ms-animation-timing-function: linear;
	-moz-animation-timing-function: linear;
	-op-animation-timing-function: linear;
	-webkit-animation-timing-function: linear;
	animation-timing-function: linear;
	-ms-animation-iteration-count: 1;
	-moz-animation-iteration-count: 1;
	-op-animation-iteration-count: 1;
	-webkit-animation-iteration-count: 1;
	animation-iteration-count: 1;
    box-shadow: 0 0 10px rgba(0,0,0, .1);
	opacity: 1;
	z-index: 999999;
}
.sticky-header .logo {
    position: relative;
    display: block;
    padding: 25px 0 25px;
}
.sticky-header .logo a{
    position: relative;
    display: inline-block;
}
.sticky-header .main-menu .navigation> li {
    margin-right: 50px;
}
.sticky-header .main-menu .navigation> li> a {
    padding: 33px 0px 37px;
}
.sticky-header .main-menu .navigation>li:hover>a,
.sticky-header .main-menu .navigation>li.current>a{
    color: var(--thm-base);
}
.sticky-header .main-menu .navigation>li>a span:before {
    display: none;
}
.sticky-header .main-menu .navigation>li>a:before {
    display: none;
}



.header-style-one {
 
}
.header-top {
    position: relative;
    display: block;
    background: #004da1;
    padding: 13px 0;
}
.header-top .outer-box{
    position: relative;
    display: block;
}






.header-top_left{
    position: relative;
    display: block;
    padding-left: 155px;
}
.header-top_left .text{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
}
.header-top_left .text p{
    color: #ffffff;
    font-size: 15px;
    line-height: 24px;
    font-weight: 600;
    margin: 0;
    font-family: var(--thm-font-2);
}

.header-contact-info1{
    position: relative;
    display: block;
}
.header-contact-info1 ul{
    overflow: hidden;
}
.header-contact-info1 ul li{
    position: relative;
    display: block;
    float: left;
    padding-right: 20px;
    margin-right: 21px;
    color: #ffffff;
    font-size: 15px;
    line-height: 24px;
    font-weight: 600;
    font-family: var(--thm-font-2);
}
.header-contact-info1 ul li:last-child{
    margin-right: 0;
    padding-right: 0;
}
.header-contact-info1 ul li:before{
    content: "";
    position: absolute;
    top: 2px;
    right: 0;
    bottom: 2px;
    width: 1px;
    background-color: #ffffff;
    opacity: 0.20;
}
.header-contact-info1 ul li span:before{
    position: relative;
    top: 1px;
    display: inline-block;
    color: var(--thm-primary);
    font-size: 16px;
    padding-right: 8px;
}
.header-contact-info1 ul li a{
    color: #ffffff;
}



.header-top_right {
    position: relative;
    display: flex;
    align-items: center;
}
.header-mail-box{
    position: relative;
    display: flex;
    align-items: center;
}
.header-mail-box span:before{
    position: relative;
    top: -1px;
    display: inline-block;
    color: var(--thm-primary);
    padding-right: 9px;
}
.header-mail-box p{
    position: relative;
    color: #ffffff;
    font-size: 15px;
    line-height: 24px;
    font-weight: 600;
    margin: 0;
    font-family: var(--thm-font-2);
}
.header-mail-box p a{
    color: #ffffff;    
}

.space-box1{
    position: relative;
    display: block;
    width: 40px;
    height: 24px;
}
.space-box1:before{
    content: "";
    position: absolute;
    top: 2px;
    left: 50%;
    bottom: 2px;
    width: 1px;
    background-color: #ffffff;
    opacity: 0.20;    
}

.our-service-location-area{
    position: relative;
    display: block;
    padding-left: 25px;
    width: 135px;
}
.our-service-location-area .icon{
    position: absolute;
    top: -1px;
    left: 0;
    bottom: 0;
    color: var(--thm-primary);
}
.our-service-location-area .select-box{
    position: relative;
    display: block;
}
.our-service-location-area .select-box .nice-select {
    height: 24px;
    line-height: 24px;
    background:transparent;
    border: 0px solid #ecf2f6 !important;
    font-family: var(--thm-font-2);
    border-radius: 0px;
    font-size: 15px;
    font-weight: 600;
    color: #ffffff;
    padding-left: 0px;
    padding-right: 20px;
}
.our-service-location-area .select-box .nice-select:after {
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-bottom: 2px solid #fff;
    border-right: 2px solid #fff;
    margin-top: 5px;
    z-index: 10;
}
.our-service-location-area .select-box .nice-select .list {
    background-color: rgba(0, 0, 0, 0.95);
    border-radius: 0px;
    margin-top: 4px;
    padding: 0;
    width: 150px;
}
.our-service-location-area .select-box .nice-select .option:hover, 
.our-service-location-area .select-box .nice-select .option.focus, 
.our-service-location-area .select-box .nice-select .option.selected.focus {
    background-color: var(--thm-primary);
}
.our-service-location-area .select-box .nice-select .option {
    line-height: 30px;
    min-height: 30px;
    padding-left: 10px;
    padding-right: 10px;
}


  

.header {
    position: relative;
    display: block;
}
.header .auto-container{
    max-width: 1400px;
}
.header .outer-box {
    position: relative;
    background: #ffffff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 100px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
.header .outer-box:before{
    content: "";
    position: absolute;
    top: 30px;
    left: 30px;
    bottom: -10px;
    right: 30px;
    background: var(--thm-primary);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    z-index: -1;
}


.header-left {
    position: relative;
    display: block;
}
.header-left .logo {
    position: relative;
    display: block;
}
.header-left .logo a{
    position: relative;
    display: inline-block;
}


.header-right {
    position: relative;
    display: flex;
    align-items: center;
    z-index: 2;
}
.header-right .nav-outer {
    position: relative;
    display: block;
    float: left;
    z-index: 2;
}
.main-menu.style1{}
.main-menu {
    position: relative;
    display: block;
    float: left;
}
.main-menu .navbar-collapse{
	padding:0px;
	display:block !important;
}
.main-menu .navigation {
	position: inherit;
	display: block;
}
.main-menu .navigation> li {
    position: inherit;
    display: inline-block;
    float: left;
    margin-right: 5px;
}
.main-menu .navigation> li:last-child{
    margin-right: 0;
}
.main-menu .navigation>li>a {
    position: relative;
    display: block;
    padding: 33px 20px 37px;
    color: #151515;
    font-size: 15px;
    line-height: 30px;
    font-weight: 700;
    text-transform: uppercase;
    transition: all 500ms ease;
    font-family: var(--thm-font);
    opacity: 1;
    z-index: 1;
}
.main-menu .navigation>li:hover>a,
.main-menu .navigation>li.current>a{
    color: var(--thm-primary);
}

.main-menu .navigation>li>a span{
    position: relative;
    display: inline-block;
    z-index: 1;
}
.main-menu .navigation>li>a span:before {
    position: absolute;
    top: 7px;
    left: -10px;
    bottom: 3px;
    right: -15px;
    background: #fff;
    content: "";
    z-index: -1;
}
.main-menu .navigation>li.dropdown>a {
    padding-right: 15px;    
}
.main-menu .navigation>li.dropdown>a:after {
    position: absolute;
    right: 0px;
    font-family: Fontawesome;
    content: "\f067";
    color: #98a1a7;
    font-size: 12px;
    line-height: 16px;
    font-weight: 100;
    transition: all 200ms linear;
    transition-delay: 0.1s;
    z-index: 1;
}
.main-menu .navigation>li:hover.dropdown>a:after,
.main-menu .navigation>li.current.dropdown>a:after{
    color: var(--thm-primary);
    line-height: 36px;
}

.main-menu .navigation>li>a:before {
    font-family: 'icomoon' !important;
    content: "\e91e";
    position: absolute;
    top: 0px;
    left: 0;
    bottom: 0px;
    right: 0;
    color: var(--thm-primary);
    font-size: 50px;
    line-height: 50px;
    font-weight: 100;
    z-index: -1;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scaleY(0);
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.main-menu .navigation> li> a:hover:before,
.main-menu .navigation> li.current> a:before {
    transform: scaleY(1.0);
    transition: all 0.5s ease-in-out 0.1s;   
}

  

.main-menu .navigation> li> ul,
.main-menu .navigation> li> .megamenu {
	position: absolute;
	top: 90%;
    left: inherit;
	width: 250px;
	padding: 0px 0;
    opacity: 0;
    visibility: hidden;
    border-radius: 10px;
	-moz-transform: translateY(30px);
	-webkit-transform: translateY(30px);
	-ms-transform: translateY(30px);
	-o-transform: translateY(30px);
    transform: translateY(30px);
    transition:all 100ms ease;
	z-index: 100;
}
.main-menu .navigation> li> ul:before,
.main-menu .navigation> li> .megamenu:before{
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    content: '';
    background: var(--thm-base);
    border-radius: 10px;
	-webkit-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	-ms-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	-o-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	-moz-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
    z-index: -1;
}
.main-menu .navigation> li.dropdown:hover> ul,
.main-menu .navigation> li.dropdown:hover .megamenu{
	opacity:1;
	visibility:visible;
	-moz-transform: translateY(0);
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	-o-transform: translateY(0);
    transform: translateY(0);
	transition:all 300ms ease;
	-moz-transition:all 300ms ease;
	-webkit-transition:all 500ms ease;
	-ms-transition:all 300ms ease;
	-o-transition:all 300ms ease;
}
.main-menu .navigation> li> ul> li,
.main-menu .navigation> li> .megamenu li{
	position: relative;
    display: block;
    padding: 0 20px;
	width: 100%;
}
.main-menu .navigation> li> ul> li> a,
.main-menu .navigation> li> .megamenu li> a {
	position: relative;
	display: block;
    border-bottom: 1px solid rgba(255, 255, 255, 0.10);
	padding: 13px 0px 13px;
	color: #ffffff;
	font-size: 16px;
	line-height: 24px;
	font-weight: 500;
	text-align: left;
	text-transform: capitalize;
	transition: all 500ms ease;
    letter-spacing: 0.03em;
    font-family: var(--thm-font);
}
.main-menu .navigation> li> ul> li:last-child> a,
.main-menu .navigation> li> .megamenu li:last-child a{
    border: none;
} 
.main-menu .navigation> li> ul> li> a:before{
    font-family: FontAwesome;
    content: "\f105";
    position: absolute;
    top: 2px;
    left: 0;
    bottom: 0;
    font-size: 18px;
    line-height: 48px;
    color: var(--thm-primary);
    transform: scaleX(0);
	transition: all 500ms ease;
}
.main-menu .navigation> li> ul> li:hover> a:before{
    transform: scaleX(1.0);
}
.main-menu .navigation> li> ul> li> a:hover{
    color: var(--thm-primary); 
    padding-left: 15px; 
}
.main-menu .navigation> li> ul> li:hover > a {
    color: var(--thm-primary); 
    padding-left: 15px; 
}
.main-menu .navigation> li> ul> li.dropdown a{}
.main-menu .navigation> li> ul> li.dropdown> a:after {
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0;
    font-family: FontAwesome;
    content: "\f105";
    color: #ffffff;
    font-size: 14px;
    font-weight: 700;
    margin: 12px 0;
    text-align: right;
    z-index: 5;
}



.main-menu .navigation> li> ul> li> ul {
	position: absolute;
	top: 0%;
	left: 100%;
	width: 250px;
	padding: 0;
	display: none;
    border-left: 10px solid transparent;
	background: var(--thm-base);
    border-radius: 10px;
	-webkit-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	-ms-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	-o-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	-moz-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
	box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
    -moz-transform: translateY(30px);
	-webkit-transform: translateY(30px);
	-ms-transform: translateY(30px);
	-o-transform: translateY(30px);
    transform: translateY(30px);
    transition:all 100ms ease;
	z-index: 100;
}
.main-menu .navigation li> ul> li.dropdown:hover ul {
	opacity:1;
	visibility:visible;
	-moz-transform: translateY(0);
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	-o-transform: translateY(0);
	transform: translateY(0);
	transition:all 300ms ease;
	-moz-transition:all 300ms ease;
	-webkit-transition:all 500ms ease;
	-ms-transition:all 300ms ease;
	-o-transition:all 300ms ease;	
}
.main-menu .navigation>li>ul>li>ul> li {
	position: relative;
    display: block;
    padding: 0 20px;
	width: 100%;
}
.main-menu .navigation> li> ul> li> ul> li> a {
	position: relative;
	display: block;
    border-bottom: 1px solid rgba(255, 255, 255, 0.10);
	padding: 13px 0px 13px;
	color: #ffffff;
	font-size: 16px;
	line-height: 24px;
	font-weight: 500;
	text-align: left;
	text-transform: capitalize;
	transition: all 500ms ease;
    letter-spacing: 0.03em;
    font-family: var(--thm-font);
}
.main-menu .navigation> li> ul> li> ul> li:last-child> a{
    border: none;
}
.main-menu .navigation> li> ul> li> ul> li> a:hover {
    color: var(--thm-primary); 
    padding-left: 15px;
}
.main-menu .navigation> li> ul> li> ul> li> a:before{
    font-family: FontAwesome;
    content: "\f105";
    position: absolute;
    top: 2px;
    left: 0;
    bottom: 0;
    font-size: 18px;
    line-height: 48px;
    color: var(--thm-primary);
    transform: scaleX(0);
	transition: all 500ms ease;
}
.main-menu .navigation> li> ul> li> ul> li:hover> a:before{
    transform: scaleX(1.0);
}




/** Megamenu Style **/
.main-menu .navigation > li.dropdown > .megamenu {
    position: absolute;
    width: 100%;
    padding: 0px 0px;
    left: 0px;
}
.main-menu .navigation li.dropdown .megamenu li h4{
  font-weight: 500;
  padding: 3px 0px;
  color: #fff;
}
.main-menu .navbar-collapse>ul li.dropdown .dropdown-btn {
    position: absolute;
    right: 0px;
    top: 0;
    width: 50px;
    height: 42px;
    border-left: 1px solid #04102a;
    text-align: center;
    font-size: 16px;
    line-height: 42px;
    color: #ffffff;
    cursor: pointer;
    display: none;
    z-index: 5;
}



.shopping-cart-box{
    position: relative;
    display: block;
}
.shopping-cart-box a{
    position: relative;
    display: block;
    width: 42px;
    height: 42px;
    border: 2px solid var(--thm-base);
    border-radius: 50%;
    color: var(--thm-base);
    font-size: 18px;
    line-height: 38px;
    text-align: center;
    -webkit-transition: all 0.4s linear;
    -o-transition: all 0.4s linear;
    transition: all 0.4s linear;    
}
.shopping-cart-box a:hover{
    border: 2px solid var(--thm-primary);
}
.shopping-cart-box a .count {
    position: absolute;
    top: -5px;
    right: -3px;
    width: 15px;
    height: 16px;
    background: var(--thm-base);
    color: #fff;
    font-size: 10px;
    line-height: 15px;
    border-radius: 50%;
    font-family: var(--thm-font);
}

.space-box2{
    position: relative;
    display: block;
    width: 40px;
    height: 42px;
}
.space-box2:before{
    position: absolute;
    top: 0;
    left: 50%;
    bottom: 0;
    width: 1px;
    background: #dae5ec;
    content: "";
}

.header-right_buttom {
    position: relative;
    display: block;
    margin-left: 40px;
}
.header-right_buttom .btns-box {
    position: relative;
    display: block;
    line-height: 0;
}
.header-right_buttom .btns-box a:after{
    background: #65cef5;
}
.header-right_buttom .btns-box a.btn-one .round {
    background: #5dbcdf;
}
.header-right_buttom .btns-box a.btn-one:before {
    display: none;
}



/*** 
=====================================================
	Main Header style Two Css
=====================================================
***/
.header-style-two {
    position: relative;
}
.header-top-style{
    position: relative;
    display: block;
    background: var(--thm-base);
}
.header-top-style .auto-container{
    max-width: 100%;
    padding: 0 40px;
}
.header-top-style .outer-box{
    position: relative;
    display: block;
}

.header-top_left--style2 {
    position: relative;
    display: block;
    padding: 13px 0 12px;
    padding-right: 30px;
    border-right: 1px solid rgba(255, 255, 255, 0.20);
}
.header-top_left--style2 .header-contact-info1{
    position: relative;
    display: block;
}
.header-top_left--style2 .header-contact-info1 ul{
    overflow: hidden;
}
.header-top_left--style2 .header-contact-info1 ul li {
    padding-right: 20px;
    margin-right: 0;
}
.header-top_left--style2 .header-contact-info1 ul li span.marker {
    position: relative;
    top: 2px;
}

.header-top_left--style2 .header-contact-info1 ul li:before {
    display: none;
}
.header-top_left--style2 .header-contact-info1 ul li:last-child{
    padding-right: 0;
}
.header-top_left--style2 .header-contact-info1 ul li a.local-dealer{
    position: relative;
    display: inline-block;
    color: var(--thm-primary);
    border-bottom: 1px solid var(--thm-primary);
    -webkit-transition: all 0.4s linear;
    -o-transition: all 0.4s linear;
    transition: all 0.4s linear;   
}
.header-top_left--style2 .header-contact-info1 ul li a.local-dealer:hover{
    color: #fff;
}


.header-top_right--style2 {
    position: relative;
    display: block;
}
.header-top_right--style2 .header-mail-box {
    position: relative;
    display: block;
    float: left;
    width: 250px;
    padding-top: 13px;
    padding-left: 21px;
    padding-bottom: 13px;
    border-left: 1px solid rgba(255, 255, 255, 0.10);
    border-right: 1px solid rgba(255, 255, 255, 0.10);
}
.header-top_right--style2 .header-mail-box span:before {
    top: 3px;
    float: left;
}


.home_or_office-service {
    position: relative;
    display: block;
    float: right;
    padding-top: 12px;
    padding-left: 20px;
    padding-bottom: 15px;
}
.home_or_office-service li{
    position: relative;
    display: inline-block;
    float: left;
    padding-right: 16px;
    margin-right: 15px;
    line-height: 20px;
}
.home_or_office-service li:last-child{
    margin-right: 0;
    padding-right: 0;
}
.home_or_office-service li:before{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 1px;
    background: #ffffff;
    content: "";
    opacity: 0.20;
}
.home_or_office-service li:last-child:before{
    display: none;
}
.home_or_office-service li a{
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
    font-family: var(--thm-font-2);
    -webkit-transition: all 0.4s linear;
    -o-transition: all 0.4s linear;
    transition: all 0.4s linear;   
}
.home_or_office-service li a:hover{
    color: var(--thm-primary);
}


.header-top_middle--style2 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    max-width: 30%;
    width: 100%;
    margin: 0 auto;
    text-align: center;
    padding: 12px 0 13px;
}
.header-top_middle--style2 h6{
    color: #ffffff;
    font-size: 15px;
    line-height: 24px;
    font-weight: 600;
}
.header-top_middle--style2 h6 span:before{
    position: relative;
    top: 2px;
    display: inline-block;
    padding-right: 7px;
    font-size: 18px;
    color: var(--thm-primary);
}



.header-style2{
    position: relative;
    display: block;
    background: #ffffff;
}
.header-style2 .auto-container{
    max-width: 100%;
    padding: 0 40px;
}
.header-style2 .outer-box {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header-left--style2 {
    position: relative;
    display: flex;
    align-items: center;
}
.header-left--style2 .logo {
    position: relative;
    display: block;
    float: left;
}
.header-left--style2 .logo a{
    position: relative;
    display: inline-block;
}

.space-box3{
    position: relative;
    display: block;
    width: 80px;
    height: 50px;
}
.space-box3:before{
    position: absolute;
    top: 0;
    left: 50%;
    bottom: 0;
    width: 1px;
    background: #dae5ec;
    content: "";
}
.header-left--style2 .nav-outer {
    position: relative;
    display: block;
    float: left;
    z-index: 2;
}

.header-right--style2{
    position: relative;
    display: flex;
    align-items: center;
    z-index: 2;
}
.header-right--style2 .header-right_buttom {
    position: relative;
    display: block;
    margin-left: 0px;
}

.header-social-link-1 {
    position: relative;
    display: block;
}
.header-social-link-1 .social-link{
    position: relative;
    display: block;
}
.header-social-link-1 .social-link ul{}
.header-social-link-1 .social-link ul li{
    position: relative;
    display: inline-block;
    float: left;
    margin-right: 10px;
}
.header-social-link-1 .social-link ul li:last-child{
    margin-right: 0;
}
.header-social-link-1 .social-link ul li a{
    position: relative;
    display: block;
    height: 42px;
    width: 42px;
    border: 2px solid #dae5ec;
    border-radius: 50%;
    color: #98a1a7;
    font-size: 18px;
    line-height: 38px;
    text-align: center;
    transition: all 200ms linear;
    transition-delay: 0.1s;
    z-index: 1;
}
.header-social-link-1 .social-link ul li a:hover{
    border-color: var(--thm-primary);
}
.header-social-link-1 .social-link ul li a:before{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: var(--thm-primary);
    content: "";
    border-radius: 50%;
    z-index: -1;
    transform: scale(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
}
.header-social-link-1 .social-link ul li:hover a:before{
    transform: scaleX(1.0);      
}
.header-social-link-1 .social-link ul li:hover a{
    color: #ffffff;
}




