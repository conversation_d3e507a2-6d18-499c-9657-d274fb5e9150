<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hero_slides', function (Blueprint $table) {
            $table->id();
            $table->text('tag_en');
            $table->text('tag_am');
            $table->text('title_en');
            $table->text('title_am');
            $table->text('content_en');
            $table->text('content_am');
            $table->string('featured_image');
            $table->string('layer_image');
            $table->string('bg_image');
            $table->string('vid_link');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hero_slides');
    }
};
