<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RecentSearch extends Model
{
    protected $fillable = [
        'keyword',
        'search_count',
        'ip_address',
        'user_agent',
    ];

    /**
     * Get recent search keywords ordered by frequency and recency
     */
    public static function getRecentKeywords($limit = 10)
    {
        return self::select('keyword')
            ->selectRaw('COUNT(*) as total_searches')
            ->selectRaw('MAX(created_at) as last_searched')
            ->where('created_at', '>=', now()->subDays(30)) // Only last 30 days
            ->groupBy('keyword')
            ->orderByRaw('COUNT(*) DESC, MAX(created_at) DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * Add or update a search keyword
     */
    public static function addKeyword($keyword, $ipAddress = null, $userAgent = null)
    {
        $keyword = trim(strtolower($keyword));

        // Don't save empty or very short keywords
        if (strlen($keyword) < 2) {
            return;
        }

        // Check if keyword exists in the last 24 hours from same IP
        $existing = self::where('keyword', $keyword)
            ->where('ip_address', $ipAddress)
            ->where('created_at', '>=', now()->subDay())
            ->first();

        if ($existing) {
            // Update existing record
            $existing->increment('search_count');
            $existing->touch(); // Update timestamp
        } else {
            // Create new record
            self::create([
                'keyword' => $keyword,
                'search_count' => 1,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
            ]);
        }

        // Clean up old records (keep only last 90 days)
        self::where('created_at', '<', now()->subDays(90))->delete();
    }
}
