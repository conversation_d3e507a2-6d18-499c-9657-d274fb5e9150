@extends('frontend.layouts.frontend')
@section('title','Home')

@section('content')
    <!-- Display Error Messages -->
    @if(session('error'))
    <div class="search-message-bar error-message" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; padding: 15px 0; text-align: center; position: relative; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 1000;">
        <div class="container">
            <div class="message-content" style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                <span class="icon-close" style="font-size: 18px;"></span>
                <strong style="font-size: 16px; font-weight: 600;">{{ session('error') }}</strong>
                <button type="button" class="close-btn" style="position: absolute; right: 20px; background: none; border: none; color: white; font-size: 24px; cursor: pointer; opacity: 0.8; transition: opacity 0.3s;" onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.8'">&times;</button>
            </div>
        </div>
    </div>
    @endif

    <!-- Display Success Messages -->
    @if(session('success'))
    <div class="search-message-bar success-message" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px 0; text-align: center; position: relative; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 1000;">
        <div class="container">
            <div class="message-content" style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                <span class="icon-check" style="font-size: 18px;"></span>
                <strong style="font-size: 16px; font-weight: 600;">{{ session('success') }}</strong>
                <button type="button" class="close-btn" style="position: absolute; right: 20px; background: none; border: none; color: white; font-size: 24px; cursor: pointer; opacity: 0.8; transition: opacity 0.3s;" onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.8'">&times;</button>
            </div>
        </div>
    </div>
    @endif

    <!-- Start Main Slider -->
<section class="main-slider style1">
    <div class="slider-box">
        <!-- Banner Carousel -->
        <div class="banner-carousel owl-theme owl-carousel">
            <!-- Slide -->
            @foreach($slides as $slide)
            @if(!empty($slide->{'title_'. app()->getLocale()}))
            <div class="slide">
                <div class="image-layer" style="background-image:url({{ asset('storage/'. $slide->layer_image) }})"></div>
                <div class="slider-bg-box" style="background-image: url({{ asset('storage/'. $slide->bg_image) }});"></div>
                <div class="slider-image"><img class="float-bob-y" src="{{ asset('storage/'. $slide->featured_image) }}" alt=""></div>
                <div class="round-box">
                    {{-- <h3>From the Heart<br> of Dire Dawa</h3>
                    <h2>Pure & Refreshing</h2>
                    <p>Inspired by Nature</p> --}}
                    {!! $slide->{'tag_'. app()->getLocale()} !!}
                </div>

                <div class="auto-container">
                    <div class="content">
                        <div class="big-title">
                            <h2>{!! $slide->{'title_'. app()->getLocale()} !!}</h2>
                        </div>
                        <div class="text">
                            <p>{!! $slide->{'content_'. app()->getLocale()} !!}</p>
                        </div>
                        <div class="btns-box">

                            <div class="slider-video-gallery">
                                <div class="icon">
                                    <a class="video-popup" title="Video Gallery" href="https://www.youtube.com/watch?v=oxsHnllk9rE">
                                        <span class="icon-play playicon"></span>
                                    </a>
                                </div>
                                <div class="title">
                                    <h6>Watch Video</h6>
                                    <p>Our Source, Our Pride</p>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
            @endif
            @endforeach
            <!-- Slide -->
            {{-- <div class="slide">
                <div class="image-layer" style="background-image:url({{ asset('assets/images/slides/slide-v1-1.jpg') }})"></div>
                <div class="slider-bg-box" style="background-image: url(assets/images/slides/slide-v1-1-bg2.jpg);"></div>
                <div class="slider-image"><img class="float-bob-y" src="assets/images/slides/slide-v1-1-image2.png" alt=""></div>
                <div class="round-box">
                    <h3>Supporting<br> Our Community</h3>
                    <h2>Sustainable Practices</h2>
                    <p>For a Brighter Future</p>
                </div>


                <div class="auto-container">
                    <div class="content">
                        <div class="big-title">
                            <h2>Committed to Sustainability<br> and Empowering Dire Dawa</h2>
                        </div>
                        <div class="text">
                            <p>Our eco-friendly bottling practices and community initiatives<br> are helping build a sustainable future for Dire Dawa and beyond.</p>
                        </div>
                        <div class="btns-box">

                            <div class="slider-video-gallery">
                                <div class="icon">
                                    <a class="video-popup" title="Video Gallery" href="https://www.youtube.com/watch?v=hv8rs4zAl5Q">
                                        <span class="icon-play playicon"></span>
                                    </a>
                                </div>
                                <div class="title">
                                    <h6>Watch Video</h6>
                                    <p>Green Practices in Action</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <!-- Slide -->
            <div class="slide">
                <div class="image-layer" style="background-image:url(assets/images/slides/slide-v1-1.jpg)"></div>
                <div class="slider-bg-box" style="background-image: url(assets/images/slides/slide-v1-1-bg1.jpg);"></div>
                <div class="slider-image"><img class="float-bob-y" src="assets/images/slides/slide-v1-1-image1.png" alt=""></div>
                <div class="round-box">
                    <h3>Hydration for<br> Every Moment</h3>
                    <h2>Always Fresh</h2>
                    <p>Perfect for Home & Office</p>
                </div>

                <div class="auto-container">
                    <div class="content">
                        <div class="big-title">
                            <h2>Refresh Yourself<br> Anytime, Anywhere</h2>
                        </div>
                        <div class="text">
                            <p>From family gatherings to office meetings, our water<br> keeps Dire Dawa hydrated with every sip.</p>
                        </div>
                        <div class="btns-box">

                            <div class="slider-video-gallery">
                                <div class="icon">
                                    <a class="video-popup" title="Video Gallery" href="https://www.youtube.com/watch?v=hv8rs4zAl5Q">
                                        <span class="icon-play playicon"></span>
                                    </a>
                                </div>
                                <div class="title">
                                    <h6>Watch Video</h6>
                                    <p>Delivering Refreshment</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
--}}
        </div>
    </div>
</section>
<!-- End Main Slider -->

<section class="choose-style2-area">
    <div class="auto-container">
        <div class="outer-box">
            <div class="row">
                <!--Start Single Choose Box Style2-->
                @php
                $iconClasses = ['icon-water-drop-1', 'icon-write-message', 'icon-truck', 'icon-hand'];
                @endphp
                @foreach($datas as $i=> $data)
                @if(!empty($data->{'title_'. app()->getLocale()}))
                <div class="col-xl-3 step1">
                    <div class="single-choose-box-style2">
                        <div class="inner">
                            <div class="outer-icon">
                                <div class="icon-bg" style="background-image: url('{{asset('storage/'. $data->icon1)}}');"></div>
                                <span class="{{ $iconClasses[$i % count($iconClasses)] }}"></span>
                            </div>
                            <div class="inner-content">
                                <div class="inner-icon">
                                    <div class="icon-bg" style="background-image: url('{{asset('storage/'. $data->icon2)}}');"></div>
                                    <span class="{{ $iconClasses[$i % count($iconClasses)] }}"></span>
                                </div>
                                <div class="title">
                                    <h3>{{ $data-> {'title_'. app()->getLocale()} }}</h3>
                                    <p>{{ $data-> {'content_'. app()->getLocale()} }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                @endforeach
                <!--End Single Choose Box Style2-->
                <!--Start Single Choose Box Style2-->
                {{-- <div class="col-xl-3 step1">
                    <div class="single-choose-box-style2">
                        <div class="inner">
                            <div class="outer-icon">
                                <div class="icon-bg" style="background-image: url(assets/images/shape/choose-style2-icon-bg1.png);"></div>
                                <span class="icon-write-message"></span>
                            </div>
                            <div class="inner-content">
                                <div class="inner-icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/choose-style2-icon-bg2.png);"></div>
                                    <span class="icon-write-message"></span>
                                </div>
                                <div class="title">
                                    <h3>No Commitment</h3>
                                    <p>Enjoy flexibility with no contracts, allowing you to focus on what truly matters.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--End Single Choose Box Style2-->
                <!--Start Single Choose Box Style2-->
                <div class="col-xl-3 step1">
                    <div class="single-choose-box-style2">
                        <div class="inner">
                            <div class="outer-icon">
                                <div class="icon-bg" style="background-image: url(assets/images/shape/choose-style2-icon-bg1.png);"></div>
                                <span class="icon-truck"></span>
                            </div>
                            <div class="inner-content">
                                <div class="inner-icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/choose-style2-icon-bg2.png);"></div>
                                    <span class="icon-truck"></span>
                                </div>
                                <div class="title">
                                    <h3>Fast Delivery</h3>
                                    <p>Count on us for reliable and timely water delivery, straight to your doorstep.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--End Single Choose Box Style2-->
                <!--Start Single Choose Box Style2-->
                <div class="col-xl-3 step1">
                    <div class="single-choose-box-style2">
                        <div class="inner">
                            <div class="outer-icon">
                                <div class="icon-bg" style="background-image: url(assets/images/shape/choose-style2-icon-bg1.png);"></div>
                                <span class="icon-hand"></span>
                            </div>
                            <div class="inner-content">
                                <div class="inner-icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/choose-style2-icon-bg2.png);"></div>
                                    <span class="icon-hand"></span>
                                </div>
                                <div class="title">
                                    <h3>Affordable Pricing</h3>
                                    <p>Access premium water solutions at prices that suit your budget.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> --}}
                <!--End Single Choose Box Style2-->
            </div>

        </div>
    </div>
</section>

{{-- About Us --}}
<section class="about-style2-area" id="about-us-section">
    <div class="about-style2_image-bg" style="background-image: url(assets/images/resources/about-style2-bg.jpg);">
        <div class="about-style2-image-box wow slideInLeft animated" data-wow-delay="100ms" data-wow-duration="2500ms" style="visibility: visible; animation-duration: 2500ms; animation-delay: 100ms; animation-name: slideInLeft;">
            @if(!empty($about_section->featured_image))
            <img src="{{asset('storage/'. $about_section->featured_image)}}" alt="">
            @endif
            <div class="thm-round-box1 js-tilt paroller" style="transform: unset; transition: transform 0.6s cubic-bezier(0, 0, 0, 1); will-change: transform;">
                @if(!empty($about_section->{ 'featured_image_caption_'. app()->getLocale()}))
                <h3>{!! $about_section->{ 'featured_image_caption_'. app()->getLocale()} !!}</h3>
                @endif
            <div class="js-tilt-glare" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; overflow: hidden;"><div class="js-tilt-glare-inner" style="position: absolute; top: 50%; left: 50%; pointer-events: none; background-image: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%); width: 320px; height: 320px; transform: rotate(180deg) translate(-50%, -50%); transform-origin: 0% 0% 0px; opacity: 0;"></div></div></div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-xl-6">

            </div>
            <div class="col-xl-6">
                <div class="about-style2_content">
                    @if(!empty($about_section->{ 'tag_'. app()->getLocale() } ))
                    <div class="sec-title">
                        <div class="sub-title">
                            <h5>{{ $about_section->{ 'tag_'. app()->getLocale() } }}</h5>
                        </div>
                        <h2>{!!  $about_section->{'title_'. app()->getLocale()}  !!}</h2>
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    @endif
                    @if(!empty($about_section->{'subtitle_'. app()->getLocale()} ))
                    <div class="inner-content">
                        <h5>{!!  $about_section->{'subtitle_'. app()->getLocale()}  !!}</h5>
                        <div class="text">
                            <p>{!! $about_section->{'content_'. app()->getLocale()} !!}</p>
                        </div>
                    </div>
                    @endif
                    <div class="bottom-box">
                        @if(!empty($about_section->{'certificate_image_caption_'. app()->getLocale()} ))
                        <div class="certification-box">
                            <div class="certification-box-bg" style="background-image: url(assets/images/shape/certification-box-bg.jpg);"></div>
                            <div class="inner">
                                <img src="{{asset('storage/' .$about_section->certificate_image)}}" alt="">
                                <h3>{!! $about_section->{'certificate_image_caption_'. app()->getLocale()} !!}</h3>
                            </div>
                        </div>
                        @endif
                        <div class="highlights-box" id="about-list">
                            <ul>
                                @foreach($about_list as $about)
                                @if(!empty($about->{'title_'. app()->getLocale()}))
                                <li class="wow fadeInRight animated" data-wow-delay="200ms" data-wow-duration="1500ms" style="visibility: visible; animation-duration: 1500ms; animation-delay: 200ms; animation-name: fadeInRight;">
                                    <div class="icon">
                                        <div class="icon-bg" style="background-image: url('{{asset('storage/'. $about->icon)}}');"></div>
                                        <span class="icon-shield"></span>
                                    </div>
                                    <div class="text">
                                        <h3>{{ $about->{'title_'. app()->getLocale()} }}</h3>
                                        <p>{{ $about->{'content_'. app()->getLocale()} }}</p>
                                    </div>
                                </li>
                                @endif
                                @endforeach

                                {{-- <li class="wow fadeInRight animated" data-wow-delay="300ms" data-wow-duration="1500ms" style="visibility: visible; animation-duration: 1500ms; animation-delay: 300ms; animation-name: fadeInRight;">
                                    <div class="icon">
                                        <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                        <span class="icon-medal"></span>
                                    </div>
                                    <div class="text">
                                        <h3>Certified</h3>
                                        <p>We ensure safe, pure water with globally recognized certifications.</p>
                                    </div>
                                </li> --}}

                            </ul>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</section>



<!--Start Fact Counter Area-->
<section class="fact-counter-area" id="fact">
    <div class="auto-container">
        <div class="row">
            <div class="col-xl-12">
                <div class="fact-counter_box">
                    <ul class="clearfix">
                        @foreach($facts as $i => $fact)
                        @if(!empty($fact->{'fact_'. app()->getLocale()}))

                        <li class="single-fact-counter wow slideInUp" data-wow-delay="00ms" data-wow-duration="1500ms">
                            <div class="border-box"><img src="assets/images/shape/fact-counter-border.png" alt=""/></div>
                            <div class="outer-box">
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="{{$fact->estimate}}">0</span>
                                    @if($i == 0)
                                    <span class="k">k</span>
                                    @elseif($i == 1)
                                    <span class="plus">+</span>
                                    @endif
                                </div>
                                <div class="title">
                                    <h6>{!! $fact->{'fact_'. app()->getLocale()} !!}</h6>
                                </div>
                            </div>
                        </li>
                        @endif
                        @endforeach
                        {{-- <li class="single-fact-counter wow slideInUp" data-wow-delay="200ms" data-wow-duration="1500ms">
                            <div class="border-box"><img src="assets/images/shape/fact-counter-border.png" alt=""/></div>
                            <div class="outer-box">
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="8">0</span>
                                    <span class="plus">+</span>
                                </div>
                                <div class="title">
                                    <h6>Years of <br>Experienced</h6>
                                </div>
                            </div>
                        </li>
                        <li class="single-fact-counter wow slideInUp" data-wow-delay="00ms" data-wow-duration="1500ms">
                            <div class="border-box"><img src="assets/images/shape/fact-counter-border.png" alt=""/></div>
                            <div class="outer-box">
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="45">0</span>
                                </div>
                                <div class="title">
                                    <h6>Professional Team <br>Members</h6>
                                </div>
                            </div>
                        </li>
                        <li class="single-fact-counter wow slideInUp" data-wow-delay="200ms" data-wow-duration="1500ms">
                            <div class="border-box"><img src="assets/images/shape/fact-counter-border.png" alt=""/></div>
                            <div class="outer-box">
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="27">0</span>
                                </div>
                                <div class="title">
                                    <h6>Currently Serving <br/>Areas in City</h6>
                                </div>
                            </div>
                        </li>
                        <li class="single-fact-counter wow slideInUp" data-wow-delay="00ms" data-wow-duration="1500ms">
                            <div class="border-box"></div>
                            <div class="outer-box">
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="760">0</span>
                                </div>
                                <div class="title">
                                    <h6>Customers <br>Happy With Us</h6>
                                </div>
                            </div>
                        </li> --}}

                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Fact Counter Area-->

<!--Start About Style1 Area-->
<section class="about-style1-area" id="about-company">
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="about-style1_top">
                    <div class="sec-title">
                        <div class="sub-title">
                            @if(!empty($about_company->{'tag_'. app()->getLocale()}))
                            <h5>{{$about_company->{'tag_'. app()->getLocale()} }}</h5>
                            @endif
                        </div>
                        @if(!empty($about_company->{'title_'. app()->getLocale()}))
                        <h2>{!! $about_company->{'title_'. app()->getLocale()} !!}</h2>
                        @endif
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    <div class="our-certification-box">
                        <div class="certificate-logo">
                            @if(!empty($about_company->certificate_image))
                            <img src="{{'storage/'. $about_company->certificate_image}}" alt="">
                            @endif
                        </div>
                        <div class="text">
                            @if(!empty($about_company->{'certificate_title_'. app()->getLocale()}))
                            <h3><span>{!! $about_company->{'certificate_title_'. app()->getLocale()} !!}</h3>
                            <p>{!! $about_company->{'certificate_content_'. app()->getLocale()} !!}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" id="company-goal">
            <div class="col-xl-12">
                <div class="about-style1_content">
                    <div class="about-style1_tab tabs-box">
                        <ul class="tab-buttons clearfix">
                            <li data-tab="#about" class="tab-btn active-btn">
                                <div class="left">
                                    <h2>01.</h2>
                                </div>
                                <div class="right">
                                    @if(!empty($company_story->{'tag_'.app()->getLocale() }))
                                    <h5>{!! $company_story->{'tag_'.app()->getLocale() } !!}</h5>
                                    @endif
                                </div>
                            </li>
                            <li data-tab="#goal" class="tab-btn">
                                <div class="left">
                                    <h2>02.</h2>
                                </div>
                                <div class="right">
                                    @if(!empty($company_goal->{'tag_'. app()->getLocale()}))
                                    <h5>{!! $company_goal->{'tag_'. app()->getLocale()} !!}</h5>
                                    @endif
                                </div>
                            </li>
                        </ul>

                        <div class="tabs-content">
                            <div class="pattern-bg" style="background-image: url(assets/images/pattern/thm-pattern-1.png);"></div>
                            <!--Tab-->
                            <div class="tab active-tab" id="about">
                                <diav class="about-style1-tab-content clearfix">
                                    @if(!empty($company_story->featured_image))
                                    <div class="about-style1-tab-content_bg" style="background-image: url('{{'storage/'.$company_story->featured_image }}');"></div>
                                    @endif
                                    <div class="inner-content">
                                        <div class="sec-title">
                                            @if(!empty($company_story->{'title_'. app()->getLocale()} ))
                                            <h2>{!! $company_story->{'title_'. app()->getLocale()} !!}</h2>
                                            @endif
                                            <div class="decor">
                                                <img src="assets/images/shape/decor.png" alt="">
                                            </div>
                                        </div>
                                        @if(!empty($company_story->{'content_'. app()->getLocale()} ))
                                        <p>{!! $company_story->{'content_'. app()->getLocale()} !!}</p>
                                        @endif
                                        <div class="btn-box">
                                            <a class="btn-two" href="/about-us"><span class="icon-right-arrow"></span>Read More</a>
                                        </div>
                                    </div>
                                </diav>
                            </div>
                            <!--Tab-->
                            <!--Tab-->
                            <div class="tab" id="goal">
                                <div class="about-style1-tab-content clearfix">
                                    @if(!empty($company_goal->featured_image))
                                    <div class="about-style1-tab-content_bg" style="background-image: url('{{'storage/'. $company_goal->featured_image}}');"></div>
                                    @endif
                                    <div class="inner-content">
                                        <div class="sec-title">
                                            @if(!empty($company_goal->{'title_'. app()->getLocale()} ))
                                            <h2>{!! $company_goal->{'title_'. app()->getLocale()} !!}</h2>
                                            @endif
                                            <div class="decor">
                                                <img src="assets/images/shape/decor.png" alt="">
                                            </div>
                                        </div>
                                        @if(!empty($company_goal->{'content_'. app()->getLocale()} ))
                                        <p>{!! $company_goal->{'content_'. app()->getLocale()} !!}</p><br>
                                        <div id="goal-detail-content" class="goal-accordion-content">
                                                    {!! $company_goal->{'detail_content_'. app()->getLocale()} !!}
                                        </div>
                                        @endif
                                        <div class="btns-box">
                                            <a class="btn-two accordion-btn" href="#goal" id="goal-read-more-btn" onclick="toggleGoalContent()">
                                                <span class="icon-right-arrow"></span>
                                                <span id="goal-btn-text">Read More</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--Tab-->
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

<!--End About Style1 Area-->

<!--Start Shop Style1 Area-->
<section class="shop-style1-area" id="shop">
    <div class="shop-top-image-box">
        <div class="inner">
            @if(!empty($shope->feature_image1))
            <img src="{{'storage/'. $shope->feature_image1}}" alt="">
            @endif
        </div>
        <div class="ice paroller">
            @if(!empty($shope->feature_image2))
            <img class="zoom-fade" src="{{'storage/'. $shope->feature_image2}}" alt="">
            @endif
        </div>
        <div class="round-box paroller-2">
            @if(!empty($shope->{'order_caption_'. app()->getLocale()} ))
            <h3>{!! $shope->{'order_caption_'. app()->getLocale()} !!}</h3>
            @endif
        </div>
    </div>
    @if(!empty($shope->{'water_mark_'. app()->getLocale()} ))
    <div class="big-title">{{ $shope->{'water_mark_'. app()->getLocale()} }}</div>
    @endif
    <div class="container" id="products">
        <div class="sec-title text-center">
            <div class="sub-title">
                @if(!empty($product_intro->{'tag_'.app()->getLocale()} ))
                <h5>{{$product_intro->{'tag_'.app()->getLocale()} }}</h5>
                @endif
            </div>
            @if(!empty($product_intro->{'title_'. app()->getLocale()} ))
            <h2>{!! $product_intro->{'title_'. app()->getLocale()} !!}</h2>
            @endif
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="theme_carousel shop-carousel_1 owl-dot-style1 owl-theme owl-carousel owl-loaded owl-drag" data-options="{&quot;loop&quot;: true, &quot;margin&quot;: 30, &quot;autoheight&quot;:true, &quot;lazyload&quot;:true, &quot;nav&quot;: false, &quot;dots&quot;: true, &quot;autoplay&quot;: true, &quot;autoplayTimeout&quot;: 6000, &quot;smartSpeed&quot;: 300, &quot;responsive&quot;:{ &quot;0&quot; :{ &quot;items&quot;: &quot;1&quot; }, &quot;600&quot; :{ &quot;items&quot; : &quot;1&quot; }, &quot;768&quot; :{ &quot;items&quot; : &quot;1&quot; } , &quot;992&quot;:{ &quot;items&quot; : &quot;2&quot; }, &quot;1200&quot;:{ &quot;items&quot; : &quot;3&quot; }}}">
                    <div class="owl-stage-outer">
                        <div class="owl-stage" style="transform: translate3d(-2400px, 0px, 0px); transition: 0.3s; width: 7600px;">
                            @foreach($product_home as $product)
                            @if(!empty($product->{'name_'. app()->getLocale()}))
                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="single-shop-item single-shop-item--style2">
                                    <div class="single-shop-item_inner">
                                        <div class="img-holder">
                                            <img src="{{asset('storage/'. $product->featured_image)}}" alt="">
                                            <div class="overlay">
                                                <span class="icon-email"></span>
                                                <a href="#">{{$product->{'tip_'.app()->getLocale()} }}</a>
                                            </div>
                                        </div>

                                        <div class="title-holder">
                                            <h3><a href="{{route('product.detail', $product->slug) }}">{{ $product->{'name_'. app()->getLocale()} }}</a></h3>

                                            <div class="btn-box">
                                                <a class="btn-one" href="{{route('product.detail', $product->slug) }}">
                                                    <div class="round"></div>
                                                    <span class="txt">Read More</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                            @endforeach


                            {{-- <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="single-shop-item single-shop-item--style2">
                                    <div class="single-shop-item_inner">
                                        <div class="img-holder">
                                            <img src="assets/images/shop/l1.png" alt="">
                                            <div class="overlay">
                                                <span class="icon-email"></span>
                                                <a href="#">Enquire</a>
                                            </div>
                                        </div>

                                        <div class="title-holder">
                                            <h3><a href="/product-details">1 Ltr Bottled Water</a></h3>

                                            <div class="btn-box">
                                                <a class="btn-one" href="/product-details">
                                                    <div class="round"></div>
                                                    <span class="txt">Read More</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="single-shop-item single-shop-item--style2">
                                    <div class="single-shop-item_inner">
                                        <div class="img-holder">
                                            <img src="assets/images/shop/l1.png" alt="">
                                            <div class="overlay">
                                                <span class="icon-email"></span>
                                                <a href="#">Enquire</a>
                                            </div>
                                        </div>

                                        <div class="title-holder">
                                            <h3><a href="/product-details">10 Ltr Bottled Water</a></h3>

                                            <div class="btn-box">
                                                <a class="btn-one" href="/product-details">
                                                    <div class="round"></div>
                                                    <span class="txt">Read More</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="owl-item" style="width: 370px; margin-right: 30px;">
                                <div class="single-shop-item single-shop-item--style2">
                                    <div class="single-shop-item_inner">
                                        <div class="img-holder">
                                            <img src="assets/images/shop/l1.png" alt="">
                                            <div class="overlay">
                                                <span class="icon-email"></span>
                                                <a href="#">Enquire</a>
                                            </div>
                                        </div>

                                        <div class="title-holder">
                                            <h3><a href="/product-details">20 Ltr Bottled Water</a></h3>s
                                            <div class="btn-box">
                                                <a class="btn-one" href="/product-details">
                                                    <div class="round"></div>
                                                    <span class="txt">Read More</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> --}}

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Shop Style1 Area-->

<!--Start Choose Style1 Area-->
<section class="choose-style1-area" id="choose-section">
    <div class="container">
        <div class="row">
            <div class="col-xl-4">
                <div class="choose-style1_image-box wow slideInLeft" data-wow-delay="100ms" data-wow-duration="2500ms">
                    @if(!empty($why_intro->featured_image))
                    <img src="{{'storage/'. $why_intro->featured_image }}" alt=""/>
                    @endif
                    <div class="round-box js-tilt paroller">
                        @if(!empty($why_intro->{'featured_image_caption_'. app()->getLocale()}))
                        <h3>{!! $why_intro->{'featured_image_caption_'. app()->getLocale()} !!}</h3>
                        @endif
                    </div>
                </div>
            </div>
            <div class="col-xl-8">
                <div class="choose-style1-content">
                    <div class="sec-title">
                        <div class="sub-title">
                            @if(!empty($why_intro->{'tag_'. app()->getLocale()} ))
                            <h5>{{$why_intro->{'tag_'. app()->getLocale()} }}</h5>
                            @endif
                        </div>
                        @if(!empty( $why_intro->{'title_'. app()->getLocale()} ))
                        <h2>{!! $why_intro->{'title_'. app()->getLocale()} !!}</h2>
                        @endif
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    <div class="inner-content">
                        <div class="shape">
                            <img src="assets/images/shape/choose-style1-shape-1.png" alt="">
                        </div>
                        <ul class="clearfix">
                            @foreach($lists-> take(2) as $i=> $list)
                            @if(!empty($list->{'title_'.app()->getLocale()}))
                            <li class="wow fadeInLeft" data-wow-delay="100ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    @if($i == 0)
                                    <span class="icon-water-drop-1"></span>
                                    @elseif($i == 1)
                                    <span class="icon-write-message"></span>
                                    @endif
                                </div>
                                <div class="text">
                                    <h3>{{ $list->{'title_'.app()->getLocale()} }}</h3>
                                    <p>{{ $list->{'content_'.app()->getLocale()} }}</p>
                                </div>
                            </li>
                            @endif
                            @endforeach
                            {{-- <li class="wow fadeInRight" data-wow-delay="100ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    <span class="icon-write-message"></span>
                                </div>
                                <div class="text">
                                    <h3>No Commitments</h3>
                                    <p>Enjoy flexibility with no contracts—just fresh water whenever you need it.</p>
                                </div>
                            </li> --}}
                        </ul>
                        <ul class="clearfix">
                            @foreach($lists-> skip(2)->take(2) as $i=> $list)
                            @if(!empty($list->{'title_'.app()->getLocale()}))
                            <li class="wow fadeInLeft" data-wow-delay="300ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    @if($i == 2)
                                    <span class="icon-shield"></span>
                                    @elseif($i == 3)
                                    <span class="icon-medal"></span>
                                    @endif
                                </div>
                                <div class="text">
                                    <h3>{{ $list->{'title_'.app()->getLocale()} }}</h3>
                                    <p>{{ $list->{'content_'.app()->getLocale()} }}</p>
                                </div>
                            </li>
                            @endif
                            @endforeach
                            {{-- <li class="wow fadeInRight" data-wow-delay="300ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    <span class="icon-medal"></span>
                                </div>
                                <div class="text">
                                    <h3>Certified Excellence</h3>
                                    <p>Certified for safety and quality, ensuring water that meets the highest standards.</p>
                                </div>
                            </li> --}}
                        </ul>
                        <ul class="clearfix">
                            @foreach($lists-> skip(4)->take(2) as $i=> $list)
                            @if(!empty($list->{'title_'.app()->getLocale()}))
                            <li class="wow fadeInLeft" data-wow-delay="500ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    @if($i == 4)
                                    <span class="icon-hand"></span>
                                    @elseif($i == 5)
                                    <span class="icon-truck"></span>
                                    @endif
                                </div>
                                <div class="text">
                                    <h3>{{ $list->{'title_'.app()->getLocale()} }}</h3>
                                    <p>{{ $list->{'content_'.app()->getLocale()} }}</p>
                                </div>
                            </li>
                            @endif
                            @endforeach
                            {{-- <li class="wow fadeInRight" data-wow-delay="600ms" data-wow-duration="1500ms">
                                <div class="icon">
                                    <div class="icon-bg" style="background-image: url(assets/images/shape/thm-shape-1.png);"></div>
                                    <span class="icon-truck"></span>
                                </div>
                                <div class="text">
                                    <h3>Quick Delivery</h3>
                                    <p>Our fast and reliable delivery ensures you never run out of fresh water.</p>
                                </div>
                            </li> --}}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!--End Choose Style1 Area-->

<!--Start Features Style1 Area-->
<section class="features-style1-area" id="features">
    <div class="auto-container">
        <div class="row">
            <div class="col-xl-6">
                <div class="features-style1_one-content">
                    @if(!empty($feature_intro->bg_image))
                    <div class="features-style1_one-content-bg" style="background-image: url('{{asset('storage/' .$feature_intro->bg_image)}}');"></div>
                    @endif
                    <div class="inner-content">
                        <div class="sec-title">
                            <div class="sub-title">
                                @if(!empty($feature_intro->{'tag_'. app()->getLocale()}))
                                <h5>{{$feature_intro->{'tag_'. app()->getLocale()} }}</h5>
                                @endif
                            </div>
                            @if(!empty($feature_intro->{'title_'. app()->getLocale()}))
                            <h2>{!! $feature_intro->{'title_'. app()->getLocale()} !!}</h2>
                            @endif
                            <div class="decor">
                                <img src="assets/images/shape/decor.png" alt="">
                            </div>
                        </div>
                        <div class="text">
                            @if(!empty($feature_intro->{'intro_'. app()->getLocale()} ))
                            <p>{{$feature_intro->{'intro_'. app()->getLocale()} }}</p>
                            @endif
                            <ul>
                                @foreach ($feature_lists as $list)
                                @if(!empty($list->{'content_'. app()->getLocale()}))
                                <li><span class="icon-water-drop"></span>{{ $list->{'content_'. app()->getLocale()} }}</li>
                                {{-- <li><span class="icon-water-drop"></span>Supports healthy digestion</li>
                                <li><span class="icon-water-drop"></span>Regulates blood pressure</li>
                                <li><span class="icon-water-drop"></span>Maintains a steady heartbeat</li> --}}
                                @endif
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            @foreach($features as $feature)
            @if(!empty($feature->{'title_'.app()->getLocale()} ))
            <div class="col-xl-3">
                <div class="features-style1_single-box">
                    <div class="features-style1_single-box-bg" style="background-image: url({{asset('storage/' .$feature->bg_image)}});"></div>
                    <div class="inner-content">
                        <h2>{!! $feature->{'title_'.app()->getLocale()} !!}</h2>
                        <p>{{ $feature->{'content_'.app()->getLocale()} }}</p>
                    </div>
                </div>
            </div>
            @endif
            @endforeach

            {{-- <div class="col-xl-3">
                <div class="features-style1_single-box style2">
                    <div class="features-style1_single-box-bg" style="background-image: url(assets/images/resources/features-style1_single-box-bg-2.jpg);"></div>
                    <div class="inner-content">
                        <h2>Convenient Solutions<br>For Every Need</h2>
                        <p>Whether it’s home, office, or events, our plans are designed to deliver fresh water hassle-free.</p>
                    </div>
                </div>
            </div> --}}

        </div>
    </div>
</section>

<!--End Features Style1 Area-->


<!--Start Working process area -->
<section class="working-process-area">
    <div class="working-process-area-bg" style="background-image: url(assets/images/parallax-background/working-process-area-bg.jpg);"></div>
    <div class="container">
        <div class="sec-title text-center">
            <div class="sub-title">
                @if(!empty($process_intro->{'tag_'. app()->getLocale()}))
                <h5>{{ $process_intro->{'tag_'. app()->getLocale()} }}</h5>
                @endif
            </div>
            @if(!empty($process_intro->{'title_'. app()->getLocale()}))
            <h2 class="clr_white">{!! $process_intro->{'title_'. app()->getLocale()} !!}</h2>
            @endif
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <!--Start Working process Single-->
            @foreach($process_cards as $i=> $card)
            @if(!empty($card->{'title_'. app()->getLocale()}))
            <div class="col-xl-4">
                <div class="single-working-process wow fadeInUp" data-wow-delay="00ms" data-wow-duration="1500ms">
                    <div class="counting-box clearfix">
                        <div class="text">
                            <h6>Step</h6>
                        </div>
                        <div class="count"></div>
                    </div>
                    <div class="content">
                        <h3>{{$card->{'title_'. app()->getLocale()} }}</h3>
                        <p>{{$card->{'content_'. app()->getLocale()} }}</p>
                    </div>
                    <div class="icon">
                        @if($i == 0)
                        <span class="icon-order"></span>
                        @elseif($i == 1)
                        <span class="icon-package"></span>
                        @elseif($i == 2)
                        <span class="icon-truck-1"></span>
                        @endif
                    </div>
                </div>
            </div>
            @endif
            @endforeach
            <!--End Working process Single-->
            <!--Start Working process Single-->
            {{-- <div class="col-xl-4">
                <div class="single-working-process wow fadeInUp" data-wow-delay="300ms" data-wow-duration="1500ms">
                    <div class="counting-box clearfix">
                        <div class="text">
                            <h6>Step</h6>
                        </div>
                        <div class="count"></div>
                    </div>
                    <div class="content">
                        <h3>Hygienic Packing</h3>
                        <p>Every bottle is packaged in a fully touchless and sterile environment to ensure purity.</p>
                    </div>
                    <div class="icon">
                        <span class="icon-package"></span>
                    </div>
                </div>
            </div>
            <!--End Working process Single-->
            <!--Start Working process Single-->
            <div class="col-xl-4">
                <div class="single-working-process wow fadeInUp" data-wow-delay="500ms" data-wow-duration="1500ms">
                    <div class="counting-box clearfix">
                        <div class="text">
                            <h6>Step</h6>
                        </div>
                        <div class="count"></div>
                    </div>
                    <div class="content">
                        <h3>Reliable Delivery</h3>
                        <p>Your order is delivered right on time to your doorstep, ready to quench your thirst.</p>
                    </div>
                    <div class="icon">
                        <span class="icon-truck-1"></span>
                    </div>
                </div>
            </div> --}}
            <!--End Working process Single-->
        </div>
    </div>
</section>


<!--End Working process area -->

<!--Start Contact Style1 Area-->
<section class="contact-style1-area" id="contact-us">
    <div class="contact-form-box1_bg" style="background-image: url(assets/images/resources/contact-form-box1_bg.jpg);"></div>
    <div class="thm-round-box1">
        <h3>Top<br> Customer<br> Support</h3>
    </div>
    <div class="gray-bg"></div>
    <div class="container">
        <div class="row">

            <div class="col-xl-6">
                <div class="contact-style1-content">
                    <div class="shape1" data-aos="fade-right" data-aos-easing="linear" data-aos-duration="2000">
                        <img class="paroller-2" src="assets/images/shape/thm-shape-2.png" alt="">
                    </div>
                    <div class="sec-title">
                        <div class="sub-title">
                            @if(!empty($contact_section->{'tag_'.app()->getLocale()}))
                            <h5>{{ $contact_section->{'tag_'.app()->getLocale()} }}</h5>
                        </div>
                        <h2>{!!$contact_section->{'title_'. app()->getLocale()}  !!}</h2>
                        @endif
                        <div class="decor">
                            <img src="assets/images/shape/decor.png" alt="">
                        </div>
                    </div>
                    <div class="inner-content">
                        <div class="quick-contact-box">
                            <div class="icon">
                                <span class="icon-calling"></span>
                            </div>
                            <div class="title">
                                @if(!empty($contact_section->{'contact_tip_'. app()->getLocale()} ))
                                <h3>{{ $contact_section->{'contact_tip_'. app()->getLocale()} }}</h3>
                                <h2><a href="tel:+50033333">{{$contact_section->contact}}</a></h2>
                                @endif
                            </div>
                        </div>
                        <div class="text">
                            @if(!empty($contact_section->{'tip_'.app()->getLocale()} ))
                            <p>{!!  $contact_section->{'tip_'.app()->getLocale()}  !!}</p>
                            @endif
                        </div>
                        <div class="btn-box">
                            <a class="btn-one" href="#">
                                <div class="round"></div>
                                <span class="txt">Call Back</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-6" id="enquire">
                <div class="contact-form-box1">
                    <div class="top-title">
                        <h2>Enquire With Our Team</h2>
                    </div>
                    <form id="contact-form" name="contact_form" class="default-form1" action="#" method="post">
                        <div class="input-box">
                            <input type="text" name="form_name" value="" placeholder="Your Name" required="">
                            <div class="icon">
                                <i class="fa fa-user" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <input type="email" name="form_email" value="" placeholder="Email Address" required="">
                            <div class="icon">
                                <i class="fa fa-envelope" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <div class="select-box">
                                <div class="round-shape"></div>
                                <select class="wide">
                                   <option data-display="Service You Need">Service You Need</option>
                                   <option value="1">Bottled Water</option>
                                   <option value="2">Water Dispenser</option>
                                   <option value="3">Water Trailers</option>
                                </select>
                            </div>
                            <div class="icon">
                                <i class="fa fa-cog" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <input type="text" name="form_address" value="" placeholder="Your Address">
                            <div class="icon">
                                <i class="fa fa-map-marker" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="button-box">
                            <div class="left">
                                <div class="checked-box1">
                                    <input type="checkbox" name="skipper1" id="skipper" checked>
                                    <label for="skipper"><span></span>I agree to receive updates<br> from Aqua Uno</label>
                                </div>
                            </div>
                            <div class="right">
                                <button class="btn-one" type="submit" data-loading-text="Please wait...">
                                    <span class="round"></span>
                                    <span class="txt">Continue</span>
                                </button>
                            </div>
                        </div>
                    </form>

                </div>
            </div>

        </div>
    </div>
</section>
<!--End Contact Style1 Area-->


<!--Start Testimonials Style1 area -->
<section class="testimonials-style1-area" id="testimonial">
    <div class="container">

        <div class="sec-title text-center">
            @if(!empty($testimonial_intro->{'tag_'. app()->getLocale()}))
            <div class="sub-title">
                <h5>{{$testimonial_intro->{'tag_'. app()->getLocale()} }}</h5>
            </div>
            <h2>{!! $testimonial_intro->{'title_'.app()->getLocale()} !!}</h2>
            @endif
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="theme_carousel testimonials-carousel_1 owl-dot-style1 owl-theme owl-carousel" data-options='{"loop": true, "margin": 30, "autoheight":true, "lazyload":true, "nav": false, "dots": true, "autoplay": true, "autoplayTimeout": 6000, "smartSpeed": 300, "responsive":{ "0" :{ "items": "1" }, "600" :{ "items" : "1" }, "768" :{ "items" : "1" } , "992":{ "items" : "1" }, "1200":{ "items" : "1" }}}'>
                    <!--Start Single Testimonials Style1-->
                    @foreach($testimonials as $testimonial)
                    @if(!empty($testimonial->{'title_'. app()->getLocale()} ))
                    <div class="single-testimonials-style1">
                        <div class="img-box">
                            <img src="{{asset('storage/'. $testimonial->profile)}}" alt=""/>
                            <div class="round-1"></div>
                            <div class="round-2"></div>
                        </div>
                        <div class="inner-content">
                            <div class="content-box">
                                <div class="rateing-box">
                                    <ul>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                    </ul>
                                </div>
                                <h3>{{ $testimonial->{'title_'. app()->getLocale()} }}</h3>
                                <p>{{$testimonial->{'content_'. app()->getLocale()} }}</p>
                                <h4>{{$testimonial->{'name_'. app()->getLocale()} }}  <span>{{$testimonial->{'address_'.app()->getLocale()} }}</span></h4>
                            </div>
                        </div>
                    </div>
                    @endif
                    @endforeach
                    <!--End Single Testimonials Style1-->
                    <!--Start Single Testimonials Style1-->
                    {{-- <div class="single-testimonials-style1">
                        <div class="img-box">
                            <img src="assets/images/testimonial/testimonial-v1-2.jpg" alt=""/>
                            <div class="round-1"></div>
                            <div class="round-2"></div>
                        </div>
                        <div class="inner-content">
                            <div class="content-box">
                                <div class="rateing-box">
                                    <ul>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                    </ul>
                                </div>
                                <h3>Great Tasting Water & Awesome</h3>
                                <p>Have used their service for five years & can say the service has always been amazing. The delivery driver is friendly. The water tastes really good & we recommend.</p>
                                <h4>Bereket Dawit, <span>Dire Dawa, Ethiopia</span></h4>
                            </div>
                        </div>
                    </div>
                    <!--End Single Testimonials Style1-->
                    <!--Start Single Testimonials Style1-->
                    <div class="single-testimonials-style1">
                        <div class="img-box">
                            <img src="assets/images/testimonial/testimonial-v1-3.jpg" alt=""/>
                            <div class="round-1"></div>
                            <div class="round-2"></div>
                        </div>
                        <div class="inner-content">
                            <div class="content-box">
                                <div class="rateing-box">
                                    <ul>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                    </ul>
                                </div>
                                <h3>Team was Very Professional</h3>
                                <p>I went to the Aqua Uno water office to speak with someone in person about Aqua Uno services. The  team was very professional and answered all my questions. </p>
                                <h4>Blen Eyob, <span>Dire Dawa, Ethiopia</span></h4>
                            </div>
                        </div>
                    </div>
                    <!--End Single Testimonials Style1-->
                    <!--Start Single Testimonials Style1-->
                    <div class="single-testimonials-style1">
                        <div class="img-box">
                            <img src="assets/images/testimonial/testimonial-v1-4.jpg" alt=""/>
                            <div class="round-1"></div>
                            <div class="round-2"></div>
                        </div>
                        <div class="inner-content">
                            <div class="content-box">
                                <div class="rateing-box">
                                    <ul>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>
                                    </ul>
                                </div>
                                <h3>The Water is Delicious</h3>
                                <p>I went to the Aqua Uno water office to speak with someone in person about Aqua Uno services. The  team was very professional and answered all my questions. </p>
                                <h4>Mengestu Tesfaye,  <span>Dire Dawa, Ethiopia</span></h4>
                            </div>
                        </div>
                    </div>
                    <!--End Single Testimonials Style1-->

                    --}}
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Testimonials Style1 area -->




<section class="partner-area" id="partners">
    <div class="partner-bg" style="background-image: url(assets/images/parallax-background/partner-bg.jpg);"></div>
    <div class="container">
        <div class="partner-slider">
            <ul class="partner-box">
                <!--Start Single Partner Logo Box-->
                @foreach($partners as $partner)
                @if(!empty($partner->logo))
                <li class="single-partner-logo-box">
                    <a href="https://gulfingot.com/"><img src="{{asset('storage/'. $partner->logo)}}" alt="Awesome Image"></a>
                </li>
                @endif
                @endforeach
                <!--End Single Partner Logo Box-->
                {{-- <li class="single-partner-logo-box">
                    <a href="#"><img src="assets/images/brand/brand-logo-2.png" alt="Awesome Image"></a>
                </li>
                <li class="single-partner-logo-box">
                    <a href="#"><img src="assets/images/brand/brand-logo-3.png" alt="Awesome Image"></a>
                </li>
                <li class="single-partner-logo-box">
                    <a href="#"><img src="assets/images/brand/brand-logo-4.png" alt="Awesome Image"></a>
                </li>

                <li class="single-partner-logo-box">
                    <a href="#"><img src="assets/images/brand/brand-logo-1.png" alt="Awesome Image"></a>
                </li>
                <!--End Single Partner Logo Box-->
                <li class="single-partner-logo-box">
                    <a href="#"><img src="assets/images/brand/brand-logo-2.png" alt="Awesome Image"></a>
                </li>
                <li class="single-partner-logo-box">
                    <a href="#"><img src="assets/images/brand/brand-logo-3.png" alt="Awesome Image"></a>
                </li>
                <li class="single-partner-logo-box">
                    <a href="#"><img src="assets/images/brand/brand-logo-4.png" alt="Awesome Image"></a>
                </li> --}}
            </ul>
        </div>
    </div>
</section>


@endsection

@push('styles')
<style>
.search-message-bar {
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.search-message-bar .close-btn:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .search-message-bar .message-content {
        flex-direction: column;
        gap: 5px;
    }

    .search-message-bar .close-btn {
        position: static;
        margin-top: 5px;
    }
}
</style>
<style>
.goal-accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out, padding 0.3s ease;
    padding: 0;
}

.goal-accordion-content.active {
    max-height: 500px; /* Adjust based on your content */
    padding: 15px 0;
}

.accordion-btn {
    transition: all 0.3s ease;
}

.accordion-btn:hover {
    transform: translateY(-2px);
}

</style>

<script>
function toggleGoalContent() {
    const content = document.getElementById('goal-detail-content');
    const btnText = document.getElementById('goal-btn-text');

    if (content.classList.contains('active')) {
        content.classList.remove('active');
        btnText.textContent = 'Read More';
    } else {
        content.classList.add('active');
        btnText.textContent = 'Close';
    }
}
</script>

@endpush

@push('scripts')
<script>
// Auto-hide message bars after 5 seconds
setTimeout(function() {
    const messageBars = document.querySelectorAll('.search-message-bar');
    messageBars.forEach(function(messageBar) {
        messageBar.style.transition = 'transform 0.5s ease-out, opacity 0.5s ease-out';
        messageBar.style.transform = 'translateY(-100%)';
        messageBar.style.opacity = '0';
        setTimeout(function() {
            messageBar.remove();
        }, 500);
    });
}, 5000);

// Handle close button clicks
document.addEventListener('DOMContentLoaded', function() {
    const closeButtons = document.querySelectorAll('.close-btn');
    closeButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const messageBar = this.closest('.search-message-bar');
            messageBar.style.transition = 'transform 0.5s ease-out, opacity 0.5s ease-out';
            messageBar.style.transform = 'translateY(-100%)';
            messageBar.style.opacity = '0';
            setTimeout(function() {
                messageBar.remove();
            }, 500);
        });
    });
});
</script>
@endpush
