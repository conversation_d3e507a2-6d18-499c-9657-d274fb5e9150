<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutPageCertificateResource\Pages;
use App\Filament\Resources\AboutPageCertificateResource\RelationManagers;
use App\Models\AboutPageCertificate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AboutPageCertificateResource extends Resource
{
    protected static ?string $model = AboutPageCertificate::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')->label('Tag (English)'),
                Forms\Components\TextInput::make('tag_am')->label('Tag (Amharic)'),
                Forms\Components\TextInput::make('title_en')->label('Title (English)'),
                Forms\Components\TextInput::make('title_am')->label('Title (Amharic)'),
                Forms\Components\FileUpload::make('image')->label('Image'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag (English)'),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag (Amharic)'),
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)'),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)'),
                Tables\Columns\ImageColumn::make('image')->label('Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutPageCertificates::route('/'),
            'create' => Pages\CreateAboutPageCertificate::route('/create'),
            'edit' => Pages\EditAboutPageCertificate::route('/{record}/edit'),
        ];
    }
}
