<?php

namespace App\Filament\Resources\EnquireContentResource\Pages;

use App\Filament\Resources\EnquireContentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEnquireContents extends ListRecords
{
    protected static string $resource = EnquireContentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
