<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class CompanyStory extends Model
{
    use Sluggable;
    protected $fillable = [
        'tag_en',
        'tag_am',
        'title_en',
        'title_am',
        'content_en',
        'content_am',
        'detail_content_en',
        'detail_content_am',
        'featured_image',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
