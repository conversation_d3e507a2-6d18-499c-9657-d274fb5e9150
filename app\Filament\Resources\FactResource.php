<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FactResource\Pages;
use App\Filament\Resources\FactResource\RelationManagers;
use App\Models\Fact;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FactResource extends Resource
{
    protected static ?string $model = Fact::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('fact_en')->label('Fact (English)'),
                Forms\Components\RichEditor::make('fact_am')->label('Fact (Amharic)'),
                Forms\Components\TextInput::make('estimate')->label('Estimated Number'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('fact_en')->label('Fact (English)'),
                Tables\Columns\TextColumn::make('fact_am')->label('Fact (Amharic)'),
                Tables\Columns\TextColumn::make('estimate')->label('Estimated Number'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacts::route('/'),
            'create' => Pages\CreateFact::route('/create'),
            'edit' => Pages\EditFact::route('/{record}/edit'),
        ];
    }
}
