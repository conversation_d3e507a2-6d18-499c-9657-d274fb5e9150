<?php

namespace App\Filament\Resources\FeatureIntroResource\Pages;

use App\Filament\Resources\FeatureIntroResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFeatureIntros extends ListRecords
{
    protected static string $resource = FeatureIntroResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
