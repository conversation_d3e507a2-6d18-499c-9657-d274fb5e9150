<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutPageWhyIntroResource\Pages;
use App\Filament\Resources\AboutPageWhyIntroResource\RelationManagers;
use App\Models\AboutPageWhyIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AboutPageWhyIntroResource extends Resource
{
    protected static ?string $model = AboutPageWhyIntro::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')->label('Tag (English)'),
                Forms\Components\TextInput::make('tag_am')->label('Tag (Amharic)'),
                Forms\Components\RichEditor::make('title_en')->label('Title (English)'),
                Forms\Components\RichEditor::make('title_am')->label('Title (Amharic)'),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag (English)'),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag (Amharic)'),
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)'),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutPageWhyIntros::route('/'),
            'create' => Pages\CreateAboutPageWhyIntro::route('/create'),
            'edit' => Pages\EditAboutPageWhyIntro::route('/{record}/edit'),
        ];
    }
}
