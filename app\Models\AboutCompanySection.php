<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class AboutCompanySection extends Model
{
    use Sluggable;
    protected $fillable = [
        'tag_en',
        'tag_am',
        'title_en',
        'title_am',
        'certificate_title_en',
        'certificate_title_am',
        'certificate_content_en',
        'certificate_content_am',
        'certificate_image',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
