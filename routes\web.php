<?php

use App\Http\Controllers\FrontendController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserMessageController;
use App\Http\Controllers\UserQuestionController;
use App\Models\GalleryCategory;
use App\Models\Product;
use App\Http\Controllers\SearchController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    $datas = app(FrontendController::class)->ShowChooseCard();
    $about_section = app(FrontendController::class)->getAboutSection();
    $about_list = app(FrontendController::class)->getAboutList();
    $facts = app(FrontendController::class)->getFact();
    $about_company = app(FrontendController::class)->getAboutCompany();
    $company_story = app(FrontendController::class)->getCompanyStory();
    $company_goal = app(FrontendController::class)->getCompanyGoal();
    $shope= app(FrontendController::class)->getShope();
    $product_intro=app(FrontendController::class)->getProductIntro();
    $why_intro=app(FrontendController::class)->getWhyIntro();
    $lists = app(FrontendController::class)->getWhyList();
    $feature_intro = app(FrontendController::class)->getFeatureIntro();
    $feature_lists =app(FrontendController::class)->getFeatureList();
    $features = app(FrontendController::class)->getFeature();
    $process_intro = app(FrontendController::class)->getProcessIntro();
    $process_cards = app(FrontendController::class)->getProcessCard();
    $contact_section = app(FrontendController::class)->getContactSection();
    $testimonial_intro = app(FrontendController::class)->getTestimonialIntro();
    $testimonials = app(FrontendController::class)->getTestimonial();
    $partners= app(FrontendController::class)->getPartner();
    $products = app(FrontendController::class)->getProduct(request());
    $product_home = app(FrontendController::class)->getProductHome();
    $site_id = app(FrontendController::class)->getSiteIdentity();
    $slides = app(FrontendController::class)->getHeroSlide();
    return view('welcome', compact('datas', 'about_section', 'about_list', 'facts', 'about_company',
    'company_story', 'company_goal', 'shope',
    'product_intro', 'why_intro', 'lists', 'feature_intro',
    'feature_lists', 'features', 'process_intro',
    'process_cards', 'contact_section', 'testimonial_intro',
     'testimonials', 'partners', 'products', 'site_id', 'slides', 'product_home'));
})->name('home');

Route::get('/about-us', function () {
    $certificate_intro = app(FrontendController::class)->getCertificateIntro();
    $certificates =app(FrontendController::class)->getCertificate();
    $why_intro = app(FrontendController::class)->getAboutPageWhyIntro();
    $why_contents = app(FrontendController::class)->getAboutPageWhyContent();
    $about_content =app(FrontendController::class)->getAboutPageContent();
    $company_story = app(FrontendController::class)->getCompanyStory();
    $site_id = app(FrontendController::class)->getSiteIdentity();
    return view('frontend/about-us', compact('certificate_intro',
    'certificates', 'why_intro', 'why_contents', 'about_content', 'site_id', 'company_story'));
});

Route::get('/services', function () {
    $site_id = app(FrontendController::class)->getSiteIdentity();
    return view('frontend/services', compact('site_id'));
});

Route::get('/products', function (Request $request) {
    // $products = app(FrontendController::class)->getProduct(request());
    // $site_id = app(FrontendController::class)->getSiteIdentity();
    $data = app(FrontendController::class)->getProduct($request);
    $site_id = app(FrontendController::class)->getSiteIdentity();
    return view('frontend.products', [
        'products' => $data['products'],
        'page' => $data['page'],
        'totalPages' => $data['totalPages'],
        'site_id' => $site_id,
    ]);
});

// Route::get('/product-details/{product}', function (Product $product) {
//     $site_id = app(FrontendController::class)->getSiteIdentity();
//     return view('frontend/product-details', compact('site_id'));
// })->name('product-detail');

Route::get('/product/{product}', [FrontendController::class, 'productDetail'])->name('product.detail');
Route::post('/product/review', [FrontendController::class, 'storeReview'])->name('product.review.store');

Route::get('/gallery', function () {
    $gallery_categories = GalleryCategory::all();
    $images = app(FrontendController::class)->getGalleries();
    $site_id = app(FrontendController::class)->getSiteIdentity();
    return view('frontend/gallery', compact('images', 'site_id', 'gallery_categories'));
});

Route::get('/faqs', function () {
    $general_faqs = app(FrontendController::class)->getGeneralFaq();
    $service_faqs = app(FrontendController::class)->getServiceFaq();
    $site_id = app(FrontendController::class)->getSiteIdentity();
    return view('frontend/faq', compact('general_faqs', 'service_faqs', 'site_id'));
});

Route::get('/contact-us', function () {
    $enquire_intro = app(FrontendController::class)->getEnquireIntro();
    $enquire_contents = app(FrontendController::class)->getEnquireContent();
    $contact = app(FrontendController::class)->getContact();
    $site_id = app(FrontendController::class)->getSiteIdentity();
    return view('frontend/contact-us', compact('enquire_intro', 'enquire_contents', 'contact', 'site_id'));
});

Route::post('/faqs', [UserQuestionController::class, 'store'])->name('user.question');
Route::post('/contact-us', [UserMessageController::class, 'store'])->name('user.message');


Route::get('/search', [SearchController::class, 'search'])->name('universal.search');

// Admin route for deleting recent searches
Route::get('/admin/recent-search/delete/{recentSearch}', function (\App\Models\RecentSearch $recentSearch) {
    $keyword = $recentSearch->keyword;
    $recentSearch->delete();

    session()->flash('success', "Search record '{$keyword}' has been deleted.");
    return redirect()->route('filament.admin.resources.recent-searches.index');
})->name('admin.recent-search.delete');


Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::get('/test', function () {
    return view('frontend/product-details');
});

require __DIR__.'/auth.php';
