

/***
=============================================
    Partner Area Css
=============================================
***/
.partner-area {
    position: relative;
    display: block;
    background: #1f2026;
    z-index: 2;
    margin-bottom: 70px; /* Adjust the value as needed */
}

.partner-bg{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-attachment: scroll;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: cover;
    z-index: -1;
}
.partner-bg:before{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgba(var(--thm-base-rgb), .9);
    content: "";
}


.partner-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.partner-box .single-partner-logo-box {
    padding: 10px;
    max-width: 100%;
    text-align: center;
}
.partner-box .single-partner-logo-box img {
    width: 100%;
    max-width: 150px; /* Adjust logo size as needed */
}

.single-partner-logo-box {
    position: relative;
    display: block;
    max-width: 25%;
    width: 100%;
    text-align: center;
    z-index: 1;
}
.single-partner-logo-box:before{
    position: absolute;
    top: 30px;
    right: 0;
    bottom: 30px;
    width: 1px;
    background: #ffffff;
    content: "";
    opacity: 0.20;
}
.single-partner-logo-box:last-child:before{
    display: none;
}

.single-partner-logo-box a {
    position: relative;
    display: inline-block;
    overflow: hidden;
}
.single-partner-logo-box a img {
    opacity: 1;
    transition: all 0.2s ease-in-out 0.1s;
    filter: grayscale(0%);
}
.single-partner-logo-box:hover a img{
    opacity: 0.40;
    filter: grayscale(0%);
    transition: all 0.8s ease-in-out 0.1s;
}



