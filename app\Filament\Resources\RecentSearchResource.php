<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RecentSearchResource\Pages;
use App\Filament\Resources\RecentSearchResource\RelationManagers;
use App\Models\RecentSearch;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RecentSearchResource extends Resource
{
    protected static ?string $model = RecentSearch::class;

    protected static ?string $navigationIcon = 'heroicon-o-magnifying-glass';

    protected static ?string $navigationLabel = 'Recent Searches';

    protected static ?string $modelLabel = 'Recent Search';

    protected static ?string $pluralModelLabel = 'Recent Searches';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('keyword')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('search_count')
                    ->required()
                    ->numeric()
                    ->default(1),
                Forms\Components\TextInput::make('ip_address')
                    ->maxLength(255),
                Forms\Components\Textarea::make('user_agent')
                    ->maxLength(65535)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('keyword')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('search_count')
                    ->numeric()
                    ->sortable()
                    ->label('Count'),
                Tables\Columns\TextColumn::make('ip_address')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label('First Search'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Last Search'),
            ])
            ->filters([
                Tables\Filters\Filter::make('recent')
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDays(7)))
                    ->label('Last 7 days'),
                Tables\Filters\Filter::make('popular')
                    ->query(fn (Builder $query): Builder => $query->where('search_count', '>=', 2))
                    ->label('Popular (2+ searches)'),
            ])
            ->actions([
                Tables\Actions\Action::make('delete')
                    ->label('Delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->action(function ($record) {
                        $record->delete();
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('cleanup_old')
                    ->label('Cleanup Old Records')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Cleanup Old Search Records')
                    ->modalDescription('This will delete all search records older than 30 days. Are you sure?')
                    ->modalSubmitActionLabel('Yes, cleanup')
                    ->action(function () {
                        $deleted = \App\Models\RecentSearch::where('created_at', '<', now()->subDays(30))->delete();
                        \Filament\Notifications\Notification::make()
                            ->title('Cleanup completed')
                            ->body("Deleted {$deleted} old search records.")
                            ->success()
                            ->send();
                    }),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRecentSearches::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false; // Disable manual creation since searches are auto-generated
    }

    public static function canEdit($record): bool
    {
        return false; // Disable editing since these are auto-generated records
    }

    public static function canDelete($record): bool
    {
        return true; // Allow deletion for cleanup purposes
    }

    public static function canDeleteAny(): bool
    {
        return true; // Allow bulk deletion
    }
}
