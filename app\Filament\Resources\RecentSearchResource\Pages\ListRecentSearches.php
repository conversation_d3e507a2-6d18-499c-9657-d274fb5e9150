<?php

namespace App\Filament\Resources\RecentSearchResource\Pages;

use App\Filament\Resources\RecentSearchResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRecentSearches extends ListRecords
{
    protected static string $resource = RecentSearchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
