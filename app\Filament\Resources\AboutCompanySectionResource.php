<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutCompanySectionResource\Pages;
use App\Filament\Resources\AboutCompanySectionResource\RelationManagers;
use App\Models\AboutCompanySection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AboutCompanySectionResource extends Resource
{
    protected static ?string $model = AboutCompanySection::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')
                    ->label('Tag (English)')
                    ->required(),
                Forms\Components\TextInput::make('tag_am')
                    ->label('Tag (Amharic)')
                    ->required(),
                Forms\Components\RichEditor::make('title_en')
                    ->label('Title (English)')
                    ->required(),
                Forms\Components\RichEditor::make('title_am')
                    ->label('Title (Amharic)')
                    ->required(),
                Forms\Components\RichEditor::make('certificate_title_en')
                    ->label('Certificate Title (English)')
                    ->required(),
                Forms\Components\RichEditor::make('certificate_title_am')
                    ->label('Certificate Title (Amharic)')
                    ->required(),
                Forms\Components\Textarea::make('certificate_content_en')
                    ->label('Content (English)')
                    ->required(),
                Forms\Components\Textarea::make('certificate_content_am')
                    ->label('Content (Amharic)')
                    ->required(),
                Forms\Components\FileUpload::make('certificate_image')
                    ->label('Certificat Image')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')
                    ->label('Tag (English)'),
                Tables\Columns\TextColumn::make('tag_am')
                    ->label('Tag (Amharic)'),
                Tables\Columns\TextColumn::make('title_en')
                    ->label('Title (English)'),
                Tables\Columns\TextColumn::make('title_am')
                    ->label('Title (Amharic)'),
                Tables\Columns\TextColumn::make('certificate_title_en')
                    ->label('Certificate Title (English)'),
                Tables\Columns\TextColumn::make('certificate_title_am')
                    ->label('Certificate Title (Amharic)'),
                Tables\Columns\TextColumn::make('certificate_content_en')->label('Content (English)'),
                Tables\Columns\TextColumn::make('certificate_content_am')->label('Content (Amharic)'),
                Tables\Columns\ImageColumn::make('certificate_image')
                    ->label('Certificate Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutCompanySections::route('/'),
            'create' => Pages\CreateAboutCompanySection::route('/create'),
            'edit' => Pages\EditAboutCompanySection::route('/{record}/edit'),
        ];
    }
}
