<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductIntroResource\Pages;
use App\Filament\Resources\ProductIntroResource\RelationManagers;
use App\Models\ProductIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductIntroResource extends Resource
{
    protected static ?string $model = ProductIntro::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')->label('Tag En'),
                Forms\Components\TextInput::make('tag_am')->label('Tag Am'),
                Forms\Components\RichEditor::make('title_en')->label('Title En'),
                Forms\Components\RichEditor::make('title_am')->label('Title Am'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag En'),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag Am'),
                Tables\Columns\TextColumn::make('title_en')->label('Title En'),
                Tables\Columns\TextColumn::make('title_am')->label('Title Am'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductIntros::route('/'),
            'create' => Pages\CreateProductIntro::route('/create'),
            'edit' => Pages\EditProductIntro::route('/{record}/edit'),
        ];
    }
}
