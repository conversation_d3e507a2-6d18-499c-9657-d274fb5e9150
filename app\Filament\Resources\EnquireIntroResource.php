<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EnquireIntroResource\Pages;
use App\Filament\Resources\EnquireIntroResource\RelationManagers;
use App\Models\EnquireIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EnquireIntroResource extends Resource
{
    protected static ?string $model = EnquireIntro::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')
                    ->label('Tag En')
                    ->required(),
                Forms\Components\TextInput::make('tag_am')
                    ->label('Tag Am')
                    ->required(),
                Forms\Components\RichEditor::make('title_en')
                    ->label('Title En')
                    ->required(),
                Forms\Components\RichEditor::make('title_am')
                    ->label('Title Am')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag English')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag Amharic')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title_en')->label('Title English')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title Amharic')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEnquireIntros::route('/'),
            'create' => Pages\CreateEnquireIntro::route('/create'),
            'edit' => Pages\EditEnquireIntro::route('/{record}/edit'),
        ];
    }
}
