<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_page_contents', function (Blueprint $table) {
            $table->id();
            $table->string('featured_image')->nullable();
            $table->string('tag_en');
            $table->string('tag_am');
            $table->string('title_en');
            $table->string('title_am');
            $table->string('sub_title_en');
            $table->string('sub_title_am');
            $table->text('description_en');
            $table->text('description_am');
            $table->string('story_title_en');
            $table->string('story_title_am');
            $table->string('story_tip_en');
            $table->string('story_tip_am');
            $table->string('story_image')->nullable();
            $table->string('what_we_do_title_en');
            $table->string('what_we_do_title_am');
            $table->text('what_we_do_en');
            $table->text('what_we_do_am');
            $table->string('what_we_do_image')->nullable();
            $table->string('general_manager_name_en');
            $table->string('general_manager_name_am');
            $table->string('general_manager_signature_image')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_page_contents');
    }
};
