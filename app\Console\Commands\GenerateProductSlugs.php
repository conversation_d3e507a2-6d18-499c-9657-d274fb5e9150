<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use Illuminate\Support\Str;

class GenerateProductSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:generate-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for products that don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating slugs for products...');

        $products = Product::whereNull('slug')->orWhere('slug', '')->get();

        if ($products->count() === 0) {
            $this->info('All products already have slugs.');
            return;
        }

        $count = 0;
        foreach ($products as $product) {
            $product->slug = Str::slug($product->name_en);
            $product->save();
            $this->line("Generated slug for: {$product->name_en} -> {$product->slug}");
            $count++;
        }

        $this->info("Successfully generated {$count} product slugs.");
    }
}
