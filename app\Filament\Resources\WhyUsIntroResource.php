<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WhyUsIntroResource\Pages;
use App\Filament\Resources\WhyUsIntroResource\RelationManagers;
use App\Models\WhyUsIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WhyUsIntroResource extends Resource
{
    protected static ?string $model = WhyUsIntro::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')->label('Tag (English)')->required(),
                Forms\Components\TextInput::make('tag_am')->label('Tag (Amharic)')->required(),
                Forms\Components\RichEditor::make('title_en')->label('Title (English)')->required(),
                Forms\Components\RichEditor::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\RichEditor::make('featured_image_caption_en')->label('Featured Image Caption (English)')->required(),
                Forms\Components\RichEditor::make('featured_image_caption_am')->label('Featured Image Caption (Amharic)')->required(),
                Forms\Components\FileUpload::make('featured_image')->label('Featured Image'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag (English)'),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag (Amharic)'),
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)'),
                Tables\Columns\TextColumn::make('title_en')->label('Title (Amharic)'),
                Tables\Columns\TextColumn::make('featured_image_caption_en')->label('Featured Image Caption (English)'),
                Tables\Columns\TextColumn::make('featured_image_caption_am')->label('Featured Image Caption (Amharic)'),
                Tables\Columns\ImageColumn::make('featured_image')->label('Featured Image'),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWhyUsIntros::route('/'),
            'create' => Pages\CreateWhyUsIntro::route('/create'),
            'edit' => Pages\EditWhyUsIntro::route('/{record}/edit'),
        ];
    }
}
