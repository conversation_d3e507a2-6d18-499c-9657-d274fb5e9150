<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    use Sluggable;
    protected $fillable = [
        'title_en',
        'title_am',
        'content_en',
        'content_am',
        'name_en',
        'name_am',
        'address_en',
        'address_am',
        'profile',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
