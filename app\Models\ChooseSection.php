<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;


class ChooseSection extends Model
{
    use Sluggable;
    protected $fillable = [
        'title_en',
        'title_am',
        'content_en',
        'content_am',
        'icon1',
        'icon2',

    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
