<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GeneralFaqResource\Pages;
use App\Filament\Resources\GeneralFaqResource\RelationManagers;
use App\Models\GeneralFaq;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GeneralFaqResource extends Resource
{
    protected static ?string $model = GeneralFaq::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Textarea::make('question_en')->label('Question (English)'),
                Forms\Components\Textarea::make('question_am')->label('Question (Amharic)'),
                Forms\Components\Textarea::make('answer_en')->label('Answer (English)'),
                Forms\Components\Textarea::make('answer_am')->label('Answer (Amharic)'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('question_en')->label('Question (English)'),
                Tables\Columns\TextColumn::make('question_am')->label('Question (Amharic)'),
                Tables\Columns\TextColumn::make('answer_en')->label('Answer (English)'),
                Tables\Columns\TextColumn::make('answer_am')->label('Answer (Amharic)'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGeneralFaqs::route('/'),
            'create' => Pages\CreateGeneralFaq::route('/create'),
            'edit' => Pages\EditGeneralFaq::route('/{record}/edit'),
        ];
    }
}
