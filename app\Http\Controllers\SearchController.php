<?php

namespace App\Http\Controllers;

use App\Models\AboutCompanySection;
use App\Models\AboutList;
use App\Models\AboutPageCertificate;
use App\Models\AboutPageCertificateIntro;
use App\Models\AboutPageContent;
use App\Models\AboutPageWhyContent;
use App\Models\AboutPageWhyIntro;
use App\Models\AboutSection;
use App\Models\ChooseSection;
use App\Models\CompanyGoal;
use App\Models\CompanyStory;
use App\Models\Contact;
use App\Models\ContactSection;
use App\Models\EnquireContent;
use App\Models\EnquireIntro;
use App\Models\Fact;
use App\Models\Feature;
use App\Models\FeatureIntro;
use App\Models\FeatureList;
use App\Models\Gallery;
use App\Models\GeneralFaq;
use App\Models\HeroSlide;
use App\Models\Partner;
use App\Models\ProcessCard;
use App\Models\ProcessIntro;
use App\Models\Product;
use App\Models\ProductIntro;
use App\Models\ServiceFaq;
use App\Models\ShopSection;
use App\Models\SiteIdentity;
use App\Models\Testimonial;
use App\Models\TestimonialIntro;
use App\Models\RecentSearch;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    private function getAboutUsData()
    {
        $frontendController = new FrontendController();
        return [
            'certificate_intro' => $frontendController->getCertificateIntro(),
            'certificates' => $frontendController->getCertificate(),
            'why_intro' => $frontendController->getAboutPageWhyIntro(),
            'why_contents' => $frontendController->getAboutPageWhyContent(),
            'about_content' => $frontendController->getAboutPageContent(),
            'site_id' => $frontendController->getSiteIdentity(),
        ];
    }

    private function getFaqData()
    {
        $frontendController = new FrontendController();
        return [
            'general_faqs' => $frontendController->getGeneralFaq(),
            'service_faqs' => $frontendController->getServiceFaq(),
            'site_id' => $frontendController->getSiteIdentity(),
        ];
    }

    private function getContactUsData()
    {
        $frontendController = new FrontendController();
        return [
            'enquire_intro' => $frontendController->getEnquireIntro(),
            'enquire_contents' => $frontendController->getEnquireContent(),
            'contact' => $frontendController->getContact(),
            'site_id' => $frontendController->getSiteIdentity(),
        ];
    }

    private function getProductsData($request)
    {
        $frontendController = new FrontendController();
        $data = $frontendController->getProduct($request);
        return [
            'products' => $data['products'],
            'page' => $data['page'],
            'totalPages' => $data['totalPages'],
            'site_id' => $frontendController->getSiteIdentity(),
        ];
    }

    public function search(Request $request){
        $query = $request->query('q');

        // Save search keyword if query exists
        if (!empty($query)) {
            RecentSearch::addKeyword(
                $query,
                $request->ip(),
                $request->userAgent()
            );
        }
        $about_company_section_result = AboutCompanySection::query();
        $about_list_result = AboutList::query();
        $about_page_certificate_result = AboutPageCertificate::query();
        $about_page_certificate_intro_result = AboutPageCertificateIntro::query();
        $about_page_content_result = AboutPageContent::query();
        $about_page_why_content_result = AboutPageWhyContent::query();
        $about_page_why_intro_result = AboutPageWhyIntro::query();
        $about_section_result = AboutSection::query();
        $choose_section_result = ChooseSection::query();
        $company_goal_result = CompanyGoal::query();
        $company_story_result = CompanyStory::query();
        $contact_result = Contact::query();
        $contact_section_result = ContactSection::query();
        $enquire_content_result = EnquireContent::query();
        $enquire_intro_result = EnquireIntro::query();
        $feature_result = Feature::query();
        $feature_intro_result = FeatureIntro::query();
        $feature_list_result = FeatureList::query();
        $gallery_result = Gallery::query();
        $general_faq_result = GeneralFaq::query();
        $hero_slide_result = HeroSlide::query();
        $partner_result = Partner::query();
        $process_card_result = ProcessCard::query();
        $process_intro_result = ProcessIntro::query();
        $product_result = Product::query();
        $product_intro_result = ProductIntro::query();
        $service_faq_result = ServiceFaq::query();
        $shop_section_result = ShopSection::query();
        $site_identity_result = SiteIdentity::query();
        $testimonial_result = Testimonial::query();
        $testimonial_intro_result = TestimonialIntro::query();
        $fact_result = Fact::query();
        $company_story_result = CompanyStory::query();


        if ($query) {
            $about_company_section_result = $about_company_section_result->where(function ($q) use ($query) {
                $q->where('tag_en', 'LIKE', '%' . $query . '%')
                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%')
                    ->orWhere('title_en', 'LIKE', '%' . $query . '%')
                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                    ->orWhere('certificate_title_en', 'LIKE', '%' . $query . '%')
                    ->orWhere('certificate_title_am', 'LIKE', '%' . $query . '%')
                    ->orWhere('certificate_content_en', 'LIKE', '%' . $query . '%')
                    ->orWhere('certificate_content_am', 'LIKE', '%' . $query . '%');
                });

                    $about_company_section_result = $about_company_section_result->get();

                    if($about_company_section_result->count() == 0){
                    $about_list_result = $about_list_result->where(function ($q) use ($query) {
                        $q->where('title_en', 'LIKE', '%' . $query . '%')
                            ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                            ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                            ->orWhere('content_am', 'LIKE', '%' . $query . '%');
                        });

                        $about_list_result = $about_list_result->get();
                        if($about_list_result->count() == 0){
                            $about_page_certificate_result = $about_page_certificate_result->where(function ($q) use ($query) {
                                $q->where('title_en', 'LIKE', '%' . $query . '%')
                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                    ->orWhere('tag_en', 'LIKE', '%' . $query . '%')
                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%');
                                });

                                $about_page_certificate_result = $about_page_certificate_result->get();

                                if($about_page_certificate_result->count() == 0){
                                    $about_page_certificate_intro_result = $about_page_certificate_intro_result->where(function ($q) use ($query) {
                                        $q->where('title_en', 'LIKE', '%' . $query . '%')
                                            ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                            ->orWhere('tag_en', 'LIKE', '%' . $query . '%')
                                            ->orWhere('tag_am', 'LIKE', '%' . $query . '%');
                                        });

                                        $about_page_certificate_intro_result = $about_page_certificate_intro_result->get();

                                        if($about_page_certificate_intro_result->count() == 0){
                                            $about_page_content_result = $about_page_content_result->where(function ($q) use ($query) {
                                                $q->where('tag_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('title_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('sub_title_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('sub_title_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('description_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('description_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('story_title_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('story_title_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('story_tip_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('story_tip_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('what_we_do_title_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('what_we_do_title_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('what_we_do_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('what_we_do_am', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('general_manager_name_en', 'LIKE', '%' . $query . '%')
                                                    ->orWhere('general_manager_name_am', 'LIKE', '%' . $query . '%');
                                                    });

                                                $about_page_content_result = $about_page_content_result->get();

                                                  if($about_page_content_result->count() == 0){
                                                    $about_page_why_content_result = $about_page_why_content_result->where(function ($q) use ($query) {
                                                        $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                            ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                            ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                            ->orWhere('content_am', 'LIKE', '%' . $query . '%');
                                                        });

                                                        $about_page_why_content_result = $about_page_why_content_result->get();

                                                             if($about_page_why_content_result->count() == 0){
                                                                $about_page_why_intro_result = $about_page_why_intro_result->where(function ($q) use ($query) {
                                                                    $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                    ->orWhere('tag_en', 'LIKE', '%' . $query . '%')
                                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%');
                                                                });

                                                                $about_page_why_intro_result = $about_page_why_intro_result->get();


                                                                if($about_page_why_intro_result->count() == 0){
                                                                    $about_section_result = $about_section_result->where(function ($q) use ($query) {
                                                                        $q->where('tagline_en', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('tagline_am', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('title_en', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('subtitle_en', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('subtitle_am', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('content_am', 'LIKE', '%' . $query . '%')
                                                                            ->orwhere('featured_image_caption_en', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('featured_image_caption_am', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('certificate_image_caption_en', 'LIKE', '%' . $query . '%')
                                                                            ->orWhere('certificate_image_caption_am', 'LIKE', '%' . $query . '%');
                                                                        });

                                                                        $about_section_result = $about_section_result->get();

                                                                            if($about_section_result->count() == 0){
                                                                            $choose_section_result = $choose_section_result->where(function ($q) use ($query) {
                                                                                $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                    ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                                                    ->orWhere('content_am', 'LIKE', '%' . $query . '%');
                                                                                });

                                                                                $choose_section_result = $choose_section_result->get();

                                                                                    if($choose_section_result->count() == 0){
                                                                                    $company_goal_result = $company_goal_result->where(function ($q) use ($query) {
                                                                                        $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                            ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                            ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                                                            ->orWhere('content_am', 'LIKE', '%' . $query . '%')
                                                                                            ->orwhere('tag_en', 'LIKE', '%' . $query . '%')
                                                                                            ->orWhere('tag_am', 'LIKE', '%' . $query . '%')
                                                                                            ->orWhere('detail_content_en', 'LIKE', '%' . $query . '%')
                                                                                            ->orWhere('detail_content_am', 'LIKE', '%' . $query . '%');
                                                                                        });

                                                                                        $company_goal_result = $company_goal_result->get();

                                                                                            if($company_goal_result->count() == 0){
                                                                                            $company_story_result = $company_story_result->where(function ($q) use ($query) {
                                                                                                $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                    ->orwhere('tag_en', 'LIKE', '%' . $query . '%')
                                                                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%')
                                                                                                    ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                                                                    ->orWhere('content_am', 'LIKE', '%' . $query . '%')
                                                                                                    ->orWhere('detail_content_en', 'LIKE', '%' . $query . '%')
                                                                                                    ->orWhere('detail_content_am', 'LIKE', '%' . $query . '%');
                                                                                                });

                                                                                                $company_story_result = $company_story_result->get();


                                                                                                if($company_story_result->count() == 0){
                                                                                                    $contact_result = $contact_result->where(function ($q) use ($query) {
                                                                                                        $q->where('address', 'LIKE', '%' . $query . '%')
                                                                                                            ->orWhere('phone', 'LIKE', '%' . $query . '%')
                                                                                                            ->orWhere('email', 'LIKE', '%' . $query . '%')
                                                                                                            ->orWhere('off_hrs', 'LIKE', '%' . $query . '%');
                                                                                                        });

                                                                                                        $contact_result = $contact_result->get();

                                                                                                        if($contact_result->count() == 0){
                                                                                                            $contact_section_result = $contact_section_result->where(function ($q) use ($query) {
                                                                                                                $q->where('tag_en', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('tip_en', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('tip_am', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('contact_tip_en', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('contact_tip_am', 'LIKE', '%' . $query . '%')
                                                                                                                    ->orWhere('contact', 'LIKE', '%' . $query . '%');
                                                                                                                });

                                                                                                                $contact_section_result = $contact_section_result->get();


                                                                                                                if($contact_section_result->count() == 0){
                                                                                                                    $enquire_content_result = $enquire_content_result->where(function ($q) use ($query) {
                                                                                                                        $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                            ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                            ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                                                                                            ->orWhere('content_am', 'LIKE', '%' . $query . '%');
                                                                                                                        });

                                                                                                                        $enquire_content_result = $enquire_content_result->get();


                                                                                                                        if($enquire_content_result->count() == 0){
                                                                                                                            $enquire_intro_result = $enquire_intro_result->where(function ($q) use ($query) {
                                                                                                                                $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                                    ->orWhere('tag_en', 'LIKE', '%' . $query . '%')
                                                                                                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%');
                                                                                                                                });

                                                                                                                                $enquire_intro_result = $enquire_intro_result->get();


                                                                                                                                if($enquire_intro_result->count() == 0){
                                                                                                                                    $fact_result = $fact_result->where(function ($q) use ($query) {
                                                                                                                                        $q->where('fact_en', 'LIKE', '%' . $query . '%')
                                                                                                                                            ->orWhere('fact_am', 'LIKE', '%' . $query . '%')
                                                                                                                                            ->orWhere('estimate', 'LIKE', '%' . $query . '%');
                                                                                                                                        });

                                                                                                                                        $fact_result = $fact_result->get();

                                                                                                                                        if($fact_result->count() == 0){
                                                                                                                                            $testimonial_intro_result = $testimonial_intro_result->where(function ($q) use ($query) {
                                                                                                                                                $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                    ->orwhere('tag_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                });

                                                                                                                                                $testimonial_intro_result = $testimonial_intro_result->get();

                                                                                                                                                if($testimonial_intro_result->count() == 0){
                                                                                                                                                    $testimonial_result = $testimonial_result->where(function ($q) use ($query) {
                                                                                                                                                        $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                            ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                            ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                            ->orWhere('content_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                            ->orwhere('name_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                            ->orWhere('name_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                            ->orWhere('address_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                            ->orWhere('address_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                        });

                                                                                                                                                        $testimonial_result = $testimonial_result->get();
                                                                                                                                                        if($testimonial_result->count() == 0){
                                                                                                                                                            $partner_result = $partner_result->where(function ($q) use ($query) {
                                                                                                                                                                $q->where('name_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                    ->orWhere('name_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                    ->orWhere('link', 'LIKE', '%' . $query . '%');
                                                                                                                                                                });

                                                                                                                                                                $partner_result = $partner_result->get();
                                                                                                                                                                if($partner_result->count() == 0){
                                                                                                                                                                    $product_result = $product_result->where(function ($q) use ($query) {
                                                                                                                                                                        $q->where('name_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                            ->orWhere('name_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                            ->orWhere('tip_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                            ->orWhere('tip_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                            ->orWhere('description_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                            ->orWhere('description_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                                        });

                                                                                                                                                                        $product_result = $product_result->get();
                                                                                                                                                                        if($product_result->count() == 0){
                                                                                                                                                                            $product_intro_result = $product_intro_result->where(function ($q) use ($query) {
                                                                                                                                                                                $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                    ->orWhere('tag_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                                                });

                                                                                                                                                                                $product_intro_result = $product_intro_result->get();

                                                                                                                                                                                if($product_intro_result->count() == 0){
                                                                                                                                                                                    $service_faq_result = $service_faq_result->where(function ($q) use ($query) {
                                                                                                                                                                                        $q->where('question_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                            ->orWhere('question_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                            ->orWhere('answer_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                            ->orWhere('answer_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                                                        });

                                                                                                                                                                                        $service_faq_result = $service_faq_result->get();

                                                                                                                                                                                        if($service_faq_result->count() == 0){
                                                                                                                                                                                            $shop_section_result = $shop_section_result->where(function ($q) use ($query) {
                                                                                                                                                                                                $q->where('water_mark_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                    ->orWhere('water_mark_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                    ->orWhere('order_caption_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                    ->orWhere('order_caption_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                                                                });

                                                                                                                                                                                                $shop_section_result = $shop_section_result->get();

                                                                                                                                                                                                if($shop_section_result->count() == 0){
                                                                                                                                                                                                    $site_identity_result = $site_identity_result->where(function ($q) use ($query) {
                                                                                                                                                                                                        $q->where('description_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                            ->orWhere('description_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                                                                    });

                                                                                                                                                                                                    $site_identity_result = $site_identity_result->get();

                                                                                                                                                                                                    if($site_identity_result->count() == 0){
                                                                                                                                                                                                        $feature_result = $feature_result->where(function ($q) use ($query) {
                                                                                                                                                                                                            $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                ->orWhere('content_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                ->orWhere('content_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                                                                        });

                                                                                                                                                                                                        $feature_result = $feature_result->get();
                                                                                                                                                                                                        if($feature_result->count() == 0){
                                                                                                                                                                                                            $feature_intro_result = $feature_intro_result->where(function ($q) use ($query) {
                                                                                                                                                                                                                $q->where('title_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                    ->orWhere('title_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                    ->orWhere('tag_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                    ->orWhere('tag_am', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                    ->orWhere('intro_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                    ->orWhere('intro_am', 'LIKE', '%' . $query . '%');
                                                                                                                                                                                                                });

                                                                                                                                                                                                                $feature_intro_result = $feature_intro_result->get();

                                                                                                                                                                                                                if($feature_intro_result->count() == 0){
                                                                                                                                                                                                                    $feature_list_result = $feature_list_result->where(function ($q) use ($query) {
                                                                                                                                                                                                                        $q->where('content_en', 'LIKE', '%' . $query . '%')
                                                                                                                                                                                                                            ->orWhere('content_am', 'LIKE', '%' . $query . '%');

                                                                                                                                                                                                                        });

                                                                                                                                                                                                                        $feature_list_result = $feature_list_result->get();

                                                                                                                                                                                                                        if($feature_list_result->count() == 0){
                                                                                                                                                                                                                            return redirect()->route('home')->with('error', 'Result not found!');
                                                                                                                                                                                                                        }

                                                                                                                                                                                                                        return redirect()->to(route('home', ['q' => $query]) . '#features')
                                                                                                                                                                                                                                    ->with(['feature_list_result' => $feature_list_result]);
                                                                                                                                                                                                                    }
                                                                                                                                                                                                            }

                                                                                                                                                                                                        return redirect()->to(route('home', ['q' => $query]) . '#features')
                                                                                                                                                                                                            ->with(['feature_result' => $feature_result]);

                                                                                                                                                                                                    }

                                                                                                                                                                                                    return redirect()->to(route('home', ['q' => $query]) . '#site-identity')
                                                                                                                                                                                                        ->with(['site_identity_result' => $site_identity_result]);

                                                                                                                                                                                                }

                                                                                                                                                                                                return redirect()->to(route('home', ['q' => $query]) . '#shop')
                                                                                                                                                                                                    ->with(['shop_section_result' => $shop_section_result]);

                                                                                                                                                                                            }

                                                                                                                                                                                        $faqData = $this->getFaqData();
                                                                                                                                                                                        $faqData['query'] = $query;
                                                                                                                                                                                        return view('frontend/faq', $faqData);
                                                                                                                                                                                    }

                                                                                                                                                                                return redirect()->to(route('home', ['q' => $query]) . '#products')
                                                                                                                                                                                    ->with(['product_intro_result' => $product_intro_result]);

                                                                                                                                                                            }

                                                                                                                                                                        $productsData = $this->getProductsData($request);
                                                                                                                                                                        $productsData['query'] = $query;
                                                                                                                                                                        return view('frontend/products', $productsData);
                                                                                                                                                                    }

                                                                                                                                                                return redirect()->to(route('home', ['q' => $query]) . '#partners')
                                                                                                                                                                    ->with(['partner_result' => $partner_result]);

                                                                                                                                                            }

                                                                                                                                                        return redirect()->to(route('home', ['q' => $query]) . '#testimonial')
                                                                                                                                                            ->with(['testimonial_result' => $testimonial_result]);

                                                                                                                                                    }

                                                                                                                                                return redirect()->to(route('home', ['q' => $query]) . '#testimonial')
                                                                                                                                                    ->with(['testimonial_intro_result' => $testimonial_intro_result]);

                                                                                                                                            }

                                                                                                                                        return redirect()->to(route('home', ['q' => $query]) . '#fact')
                                                                                                                                            ->with(['fact_result' => $fact_result]);

                                                                                                                                    }

                                                                                                                                return redirect()->to(route('home', ['q' => $query]) . '#enquire')
                                                                                                                                    ->with(['enquire_intro_result' => $enquire_intro_result]);

                                                                                                                            }
                                                                                                                        return redirect()->to(route('home', ['q' => $query]) . '#enquire')
                                                                                                                            ->with(['enquire_content_result' => $enquire_content_result]);

                                                                                                                    }
                                                                                                                return redirect()->to(route('home', ['q' => $query]) . '#contact-us')
                                                                                                                    ->with(['contact_section_result' => $contact_section_result]);

                                                                                                            }

                                                                                                        $contactUsData = $this->getContactUsData();
                                                                                                        $contactUsData['query'] = $query;
                                                                                                        return view('frontend/contact-us', $contactUsData);

                                                                                                    }

                                                                                                return redirect()->to(route('home', ['q' => $query]) . '#company-goal')
                                                                                                    ->with(['company_story_result' => $company_story_result]);

                                                                                            }

                                                                                        return redirect()->to(route('home', ['q' => $query]) . '#company-goal')
                                                                                            ->with(['company_goal_result' => $company_goal_result]);

                                                                                    }


                                                                                return redirect()->to(route('home', ['q' => $query]) . '#choose-section')
                                                                                ->with(['choose_section_result' => $choose_section_result]);

                                                                            }

                                                                            return redirect()->to(route('home', ['q' => $query]) . '#about-us-section')
                                                                                ->with(['about_section_result' => $about_section_result]);
                                                                    }


                                                                $aboutUsData = $this->getAboutUsData();
                                                                $aboutUsData['query'] = $query;
                                                                return view('/frontend/about-us', $aboutUsData);
                                                            }

                                                        $aboutUsData = $this->getAboutUsData();
                                                        $aboutUsData['query'] = $query;
                                                        return view('/frontend/about-us', $aboutUsData);

                                                  }

                                                $aboutUsData = $this->getAboutUsData();
                                                $aboutUsData['query'] = $query;
                                                return view('frontend/about-us', $aboutUsData);
                                            }

                                        $aboutUsData = $this->getAboutUsData();
                                        $aboutUsData['query'] = $query;
                                        return view('frontend/about-us', $aboutUsData);
                                    }

                                    $aboutUsData = $this->getAboutUsData();
                                    $aboutUsData['query'] = $query;
                                    return view('frontend/about-us', $aboutUsData);
                        }

                        return redirect()->to(route('home', ['q' => $query]) . '#about-list')
                            ->with(['about_list_result' => $about_list_result]);

                        }

                    return redirect()->to(route('home', ['q' => $query]) . '#about-company')
                        ->with(['about_company_section_result' => $about_company_section_result]);
                }
    }
}
