<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class Partner extends Model
{
    use Sluggable;
    protected $fillable = [
        'name_en',
        'name_am',
        'link',
        'logo',

    ];
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name'
            ]
        ];
    }
}
