<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutListResource\Pages;
use App\Filament\Resources\AboutListResource\RelationManagers;
use App\Models\AboutList;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AboutListResource extends Resource
{
    protected static ?string $model = AboutList::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title_en')->label('Title (English)'),
                Forms\Components\TextInput::make('title_am')->label('Title (Amharic)'),
                Forms\Components\Textarea::make('content_en')->label('Content (English)'),
                Forms\Components\Textarea::make('content_am')->label('Content (Amharic)'),
                Forms\Components\FileUpload::make('icon')->label('Icon'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)'),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)'),
                Tables\Columns\TextColumn::make('content_en')->label('Content (English)'),
                Tables\Columns\TextColumn::make('content_am')->label('Content (Amharic)'),
                Tables\Columns\ImageColumn::make('icon')->label('Icon'),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutLists::route('/'),
            'create' => Pages\CreateAboutList::route('/create'),
            'edit' => Pages\EditAboutList::route('/{record}/edit'),
        ];
    }
}
