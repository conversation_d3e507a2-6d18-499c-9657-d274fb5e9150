<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WhyUsListResource\Pages;
use App\Filament\Resources\WhyUsListResource\RelationManagers;
use App\Models\WhyUsList;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WhyUsListResource extends Resource
{
    protected static ?string $model = WhyUsList::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title_en')->label('Title (English)')->required(),
                Forms\Components\TextInput::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\Textarea::make('content_en')->label('Content (English)')->required(),
                Forms\Components\Textarea::make('content_am')->label('Content (Amharic)')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_en')->label('Content (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_am')->label('Content (Amharic)')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWhyUsLists::route('/'),
            'create' => Pages\CreateWhyUsList::route('/create'),
            'edit' => Pages\EditWhyUsList::route('/{record}/edit'),
        ];
    }
}
