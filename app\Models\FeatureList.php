<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class FeatureList extends Model
{
    use Sluggable;
    protected $fillable = [
        'content_en',
        'content_am',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'content_en'
            ]
        ];
    }
}
