<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name_en')->label('Name (EN)'),
                Forms\Components\TextInput::make('name_am')->label('Name (AM)'),
                Forms\Components\TextInput::make('tip_en')->label('Tip (EN)'),
                Forms\Components\TextInput::make('tip_am')->label('Tip (AM)'),
                Forms\Components\TextInput::make('description_en')->label('Description (EN)'),
                Forms\Components\TextInput::make('description_am')->label('Description (AM)'),
                Forms\Components\TextInput::make('packaging_size')->label('Packaging Size'),
                Forms\Components\TextInput::make('packaging_type')->label('Packaging Type'),
                Forms\Components\TextInput::make('shelf_life')->label('Shelf Life'),
                Forms\Components\TextInput::make('processing_type')->label('Processing Type'),
                Forms\Components\TextInput::make('flavor')->label('Flavor'),
                Forms\Components\FileUpload::make('featured_image')->label('Featured Image'),
                Forms\Components\FileUpload::make('group_image1')->label('Group Image 1'),
                Forms\Components\FileUpload::make('group_image2')->label('Group Image 2'),
                Forms\Components\FileUpload::make('group_image3')->label('Group Image 3'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_en')->label('Name English'),
                Tables\Columns\TextColumn::make('name_am')->label('Name Amharic'),
                Tables\Columns\TextColumn::make('tip_en')->label('Tip English'),
                Tables\Columns\TextColumn::make('tip_am')->label('Tip Amharic'),
                Tables\Columns\TextColumn::make('description_en')->label('Description English'),
                Tables\Columns\TextColumn::make('description_am')->label('Description Amharic'),
                Tables\Columns\TextColumn::make('packaging_size')->label('Packaging Size'),
                Tables\Columns\TextColumn::make('packaging_type')->label('Packeging Type'),
                Tables\Columns\TextColumn::make('shelf_life')->label('Shelf Life'),
                Tables\Columns\TextColumn::make('processing_type')->label('Processing Life'),
                Tables\Columns\TextColumn::make('flavor')->label('Flavor'),
                Tables\Columns\ImageColumn::make('featured_image')->label('Featured Image'),
                Tables\Columns\ImageColumn::make('group_image1')->label('Group Image-1'),
                Tables\Columns\ImageColumn::make('group_image2')->label('Group Image-2'),
                Tables\Columns\ImageColumn::make('group_image3')->label('Group Image-3'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
