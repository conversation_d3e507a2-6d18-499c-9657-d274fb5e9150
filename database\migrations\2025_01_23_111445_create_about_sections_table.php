<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_sections', function (Blueprint $table) {
            $table->id();
            $table->string('tagline_en');
            $table->string('tagline_am');
            $table->string('title_en');
            $table->string('title_am');
            $table->string('subtitle_en');
            $table->string('subtitle_am');
            $table->text('content_en');
            $table->text('content_am');
            $table->string('featured_image');
            $table->string('featured_image_caption_en');
            $table->string('featured_image_caption_am');
            $table->string('certificate_image');
            $table->string('certificate_image_caption_en');
            $table->string('certificate_image_caption_am');
            $table->string('slug');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_sections');
    }
};
