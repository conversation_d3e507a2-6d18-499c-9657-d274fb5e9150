<?php

namespace App\Http\Controllers;


use Illuminate\Http\Request;
use App\Models\UserQuestion;



class UserQuestionController extends Controller
{

    public function store(Request $request){
        dd($request->all());
        $request->validate([
        'user_name' => 'required|string',
        'email' => 'required|email',
        'question_category' => 'required|in:general,service,other',
        'question' => 'required|string',
        'address' => 'nullable|string',
        ]);

        UserQuestion::create([
            'user_name' => $request->user_name,
            'email' => $request->email,
            'question_category' => $request->question_category,
            'question' => $request->question,
            'address' => $request->address,
        ]);

        return redirect()->back()->with('success', 'Your question has been submitted successfully');
    }
}
