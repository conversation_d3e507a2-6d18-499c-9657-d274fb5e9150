<?php $__env->startSection('title','About Us'); ?>

<?php $__env->startSection('content'); ?>

<!--Start breadcrumb area paroller-->
<section class="breadcrumb-area">
    <div class="breadcrumb-area-bg" style="background-image: url(<?php echo e(asset('assets/images/breadcrumb/breadcrumb-1.jpg')); ?>);"></div>
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="inner-content text-center">
                    <div class="title">
                       <h2>About Company</h2>
                    </div>
                    <div class="breadcrumb-menu">
                        <ul>
                            <li><a href="/">Home</a></li>
                            <li><i class="fa fa-angle-right" aria-hidden="true"></i></li>
                            <li class="active">About Company</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End breadcrumb area-->

<!--Start About Style3 Area-->
<section class="about-style3-area">
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="about-style3-top">
                    <?php if(!empty($about_content->featured_image)): ?>
                    <img src="<?php echo e(asset('storage/'. $about_content->featured_image)); ?>" alt="">
                    <?php endif; ?>
                </div>
                <div class="about-style3-content">
                    <div class="row">
                        <div class="col-xl-6">
                            <div class="about-style3-text-box1">
                                <div class="sec-title">
                                    <div class="sub-title">
                                        <?php if(!empty($about_content->{'tag_'. app()->getLocale()})): ?>
                                        <h5><?php echo e($about_content->{'tag_'. app()->getLocale()}); ?></h5>
                                        <?php endif; ?>
                                    </div>
                                    <?php if(!empty($about_content->{'title_'. app()->getLocale()})): ?>
                                    <h2><?php echo $about_content->{'title_'. app()->getLocale()}; ?></h2>
                                    <?php endif; ?>
                                    <div class="decor">
                                        <img src="assets/images/shape/decor.png" alt="">
                                    </div>
                                </div>

                                <div class="inner-content">
                                    <?php if(!empty($about_content->{'sub_title_'. app()->getLocale()})): ?>
                                    <h5><?php echo $about_content->{'sub_title_'. app()->getLocale()}; ?></h5>
                                    <?php endif; ?>
                                    <div class="text">
                                        <?php if(!empty($about_content->{'description_'. app()->getLocale()})): ?>
                                        <p><?php echo e($about_content->{'description_'. app()->getLocale()}); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="bottom-box">
                                        <div class="signature">
                                            <?php if(!empty($about_content->general_manager_signature_image)): ?>
                                            <img src="<?php echo e(asset('storage/' .$about_content->general_manager_signature_image)); ?>" alt="">
                                            <?php endif; ?>
                                        </div>
                                        <div class="name">
                                            <?php if(!empty($about_content->{'general_manager_name_'. app()->getLocale()})): ?>
                                            <h5><?php echo e($about_content->{'general_manager_name_'. app()->getLocale()}); ?></h5>
                                            <?php endif; ?>
                                            <span>General Manager </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-6" id="stx">
                            <div class="about-style3-text-box2">
                                <ul>
                                    <li>
                                        <div class="inner">
                                            <div class="img-box">
                                                <div class="img-inner">
                                                    <?php if(!empty($about_content->story_image)): ?>
                                                    <img src="<?php echo e(asset('storage/'. $about_content->story_image)); ?>" alt="">
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="text-box" >
                                                <?php if(!empty($about_content->{'story_title_'. app()->getLocale()})): ?>
                                                <h3><?php echo e($about_content->{'story_title_'. app()->getLocale()}); ?></h3>
                                                <p><?php echo e($about_content->{'story_tip_'. app()->getLocale()}); ?></p><br>
                                                <div id="story-detail-content" class="accordion-content">
                                                    <?php echo $company_story->{'detail_content_'. app()->getLocale()}; ?>

                                                </div>
                                                <?php endif; ?>
                                                <div class="btns-box">
                                                    <a class="btn-two accordion-btn" href="javascript:void(0);" id="story-read-more-btn" onclick="toggleStoryContent()">
                                                        <span class="icon-right-arrow"></span>
                                                        <span id="story-btn-text">Read More</span>
                                                    </a>
                                                </div>
                                            </div>

                                        </div>
                                    </li>
                                    <li>
                                        <div class="inner">
                                            <div class="img-box">
                                                <div class="img-inner">
                                                    <?php if(!empty($about_content->what_we_do_image)): ?>
                                                    <img src="<?php echo e(asset('storage/'. $about_content->what_we_do_image)); ?>" alt="">
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="text-box">
                                                <?php if(!empty($about_content->{'what_we_do_title_'. app()->getLocale()})): ?>
                                                <h3><?php echo e($about_content->{'what_we_do_title_'. app()->getLocale()}); ?></h3>
                                                <p><?php echo e($about_content->{'what_we_do_'. app()->getLocale()}); ?></p>
                                                <?php endif; ?>
                                                <div class="btns-box">
                                                    <a class="btn-two" href="#"><span class="icon-right-arrow"></span>Read More</a>
                                                </div>
                                            </div>

                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</section>
<!--End About Style3 Area-->

<!--Start Fact Counter Area-->
<section class="fact-counter-style3-area">
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="fact-counter">
                    <div class="fact-counter-bg" style="background-image: url(assets/images/parallax-background/fact-counter_box-bg.jpg);"></div>
                    <ul class="clearfix">
                        <li class="single-fact-counter-style2 wow" data-wow-delay="200ms" data-wow-duration="1500ms">
                            <div class="icon">
                                <div class="icon-bg" style="background-image: url(assets/images/shape/fact-counter-icon-bg.png);"></div>
                                <div class="icon-bg-overlay" style="background-image: url(assets/images/shape/fact-counter-icon-bg-overlay.png);"></div>
                                <span class="icon-plastic-bottle"></span>
                            </div>
                            <div class="outer-box">
                                <span class="icon-water-drop"></span>
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="6.8">0</span>
                                    <span class="k">k</span>
                                </div>
                                <div class="title">
                                    <h6>Bottled Delivered</h6>
                                </div>
                            </div>
                        </li>
                        <li class="single-fact-counter-style2 wow" data-wow-delay="200ms" data-wow-duration="1500ms">
                            <div class="icon">
                                <div class="icon-bg" style="background-image: url(assets/images/shape/fact-counter-icon-bg.png);"></div>
                                <div class="icon-bg-overlay" style="background-image: url(assets/images/shape/fact-counter-icon-bg-overlay.png);"></div>
                                <span class="icon-rating"></span>
                            </div>
                            <div class="outer-box">
                                <span class="icon-water-drop"></span>
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="8">0</span>
                                    <span class="plus">+</span>
                                </div>
                                <div class="title">
                                    <h6>Years of experience</h6>
                                </div>
                            </div>
                        </li>
                        <li class="single-fact-counter-style2 wow" data-wow-delay="200ms" data-wow-duration="1500ms">
                            <div class="icon">
                                <div class="icon-bg" style="background-image: url(assets/images/shape/fact-counter-icon-bg.png);"></div>
                                <div class="icon-bg-overlay" style="background-image: url(assets/images/shape/fact-counter-icon-bg-overlay.png);"></div>
                                <span class="icon-businessman"></span>
                            </div>
                            <div class="outer-box">
                                <span class="icon-water-drop"></span>
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="45">0</span>
                                    <span class="k">k</span>
                                </div>
                                <div class="title">
                                    <h6>Team Members</h6>
                                </div>
                            </div>
                        </li>
                        <li class="single-fact-counter-style2 wow" data-wow-delay="200ms" data-wow-duration="1500ms">
                            <div class="icon">
                                <div class="icon-bg" style="background-image: url(assets/images/shape/fact-counter-icon-bg.png);"></div>
                                <div class="icon-bg-overlay" style="background-image: url(assets/images/shape/fact-counter-icon-bg-overlay.png);"></div>
                                <span class="icon-map"></span>
                            </div>
                            <div class="outer-box">
                                <span class="icon-water-drop"></span>
                                <div class="count-outer count-box">
                                    <span class="count-text" data-speed="3000" data-stop="27">0</span>
                                </div>
                                <div class="title">
                                    <h6>Local Serving Areas</h6>
                                </div>
                            </div>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Fact Counter Area-->


<!--Start Choose Style1 Area-->
<section class="choose-style3-area">
    <div class="container">
        <div class="sec-title text-center">
            <div class="sub-title">
                <?php if(!empty($why_intro->{'tag_'. app()->getLocale()})): ?>
                <h5><?php echo e($why_intro->{'tag_'. app()->getLocale()}); ?></h5>
                <?php endif; ?>
            </div>
            <?php if(!empty($why_intro->{'title_'. app()->getLocale()})): ?>
            <h2><?php echo $why_intro->{'title_'. app()->getLocale()}; ?></h2>
            <?php endif; ?>
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="choose-style3-content">
                    <div class="theme_carousel choose-carousel_1 owl-dot-style1 owl-theme owl-carousel"
                    data-options='{
                        "loop": true,
                        "margin": 30,
                        "autoheight": true,
                        "lazyload": true,
                        "nav": false,
                        "dots": true,
                        "autoplay": true,
                        "autoplayTimeout": 6000,
                        "smartSpeed": 300,
                        "responsive": {
                            "0": { "items": "1" },
                            "600": { "items": "1" },
                            "768": { "items": "2" },
                            "992": { "items": "3" },
                            "1200": { "items": "4" }
                        }
                    }'>

                    <!-- High Quality -->
                    <?php $__currentLoopData = $why_contents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(!empty($content->{'title_'.app()->getLocale()})): ?>
                    <div class="single-choose-box-style3">
                        <div class="inner text-center">
                            <div class="count-box">0<?php echo e($i+1); ?></div>
                            <div class="icon">
                                <?php if($i==0): ?>
                                <span class="icon-water-drop-1"></span>
                                <?php elseif($i==1): ?>
                                <span class="icon-medal"></span>
                                <?php elseif($i==2): ?>
                                <span class="icon-shield"></span>
                                <?php elseif($i==3): ?>
                                <span class="icon-hand"></span>
                                <?php endif; ?>

                            </div>
                            <div class="text">
                                <h3><?php echo e($content->{'title_'.app()->getLocale()}); ?></h3>
                                <p><?php echo e($content->{'content_'.app()->getLocale()}); ?></p>
                            </div>
                            <div class="button-boder"></div>
                        </div>
                        <div class="btn-box">
                            <a href="#"><i class="fa fa-plus" aria-hidden="true"></i></a>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    
                </div>

                </div>
            </div>

        </div>
    </div>
</section>
<!--End Choose Style1 Area-->




<!--Start Certificates Area-->
<section class="certificates-area">
    <div class="container">
        <div class="sec-title text-center">
            <div class="sub-title">
                <?php if(!empty($certificate_intro->{'tag_'.app()->getLocale()})): ?>
                <h5><?php echo e($certificate_intro->{'tag_'.app()->getLocale()}); ?></h5>
                <?php endif; ?>
            </div>
            <?php if(!empty( $certificate_intro->{'title_'. app()->getLocale()})): ?>
            <h2><?php echo $certificate_intro->{'title_'. app()->getLocale()}; ?></h2>
            <?php endif; ?>
            <div class="decor">
                <img src="assets/images/shape/decor.png" alt="">
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="theme_carousel certificates-carousel_1 owl-dot-style1 owl-theme owl-carousel" data-options='{"loop": true, "margin": 30, "autoheight":true, "lazyload":true, "nav": false, "dots": true, "autoplay": true, "autoplayTimeout": 6000, "smartSpeed": 300, "responsive":{ "0" :{ "items": "1" }, "600" :{ "items" : "1" }, "768" :{ "items" : "1" } , "992":{ "items" : "2" }, "1200":{ "items" : "3" }}}'>
                    <!--Start Single Certificates Box-->
                    <?php $__currentLoopData = $certificates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $certificate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(!empty($certificate->{'tag_'.app()->getLocale() })): ?>
                    <div class="single-certificates-box text-center">
                        <div class="img-box">
                            <img src="<?php echo e(asset('storage/' .$certificate->image)); ?>" alt="">
                        </div>
                        <div class="text-holder">
                            <div class="border-box"></div>
                            <h3><?php echo e($certificate->{'tag_'. app()->getLocale()}); ?></h3>
                            <p><?php echo e($certificate->{'title_'. app()->getLocale()}); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <!--End Single Certificates Box-->
                    <!--Start Single Certificates Box-->
                    

                </div>
            </div>
        </div>
    </div>
</section>
<!--End Certificates Area-->

<style>
.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out, padding 0.3s ease;
    padding: 0;
}

.accordion-content.active {
    max-height: 500px; /* Adjust based on your content */
    padding: 15px 0;
}

.accordion-btn {
    transition: all 0.3s ease;
}

.accordion-btn:hover {
    transform: translateY(-2px);
}

</style>

<script>
function toggleStoryContent() {
    const content = document.getElementById('story-detail-content');
    const btnText = document.getElementById('story-btn-text');

    if (content.classList.contains('active')) {
        content.classList.remove('active');
        btnText.textContent = 'Read More';
    } else {
        content.classList.add('active');
        btnText.textContent = 'Close';
    }
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.frontend', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\development\aqua-uno\resources\views/frontend/about-us.blade.php ENDPATH**/ ?>