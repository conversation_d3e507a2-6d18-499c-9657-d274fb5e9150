<?php

namespace App\Filament\Resources\AboutPageWhyContentResource\Pages;

use App\Filament\Resources\AboutPageWhyContentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAboutPageWhyContents extends ListRecords
{
    protected static string $resource = AboutPageWhyContentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
