<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ChooseSectionResource\Pages;
use App\Filament\Resources\ChooseSectionResource\RelationManagers;
use App\Models\ChooseSection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChooseSectionResource extends Resource
{
    protected static ?string $model = ChooseSection::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title_en')
                    ->label('Title_English')
                    ->required(),
                Forms\Components\TextInput::make('title_am')
                    ->label('Title_Amharic')
                    ->required(),
                Forms\Components\TextInput::make('content_en')
                    ->label('Content_English')
                    ->required(),
                Forms\Components\TextInput::make('content_am')
                    ->label('Content_Amharic')
                    ->required(),
                Forms\Components\FileUpload::make('icon1')
                    ->label('Icon1')
                    ->required(),
                Forms\Components\FileUpload::make('icon2')
                    ->label('Icon2')
                    ->required(),
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_en')->label('Title_English')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title_Amharic')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content_en')->label('Content_English')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content_am')->label('Content_Amharic')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('icon1')->label('Icon1'),
                Tables\Columns\ImageColumn::make('icon2')->label('Icon2')

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChooseSections::route('/'),
            'create' => Pages\CreateChooseSection::route('/create'),
            'edit' => Pages\EditChooseSection::route('/{record}/edit'),
        ];
    }
}
