<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use Sluggable;
    protected $fillable = [
        'name_en',
        'name_am',
        'tip_en',
        'tip_am',
        'description_en',
        'description_am',
        'packaging_size',
        'packaging_type',
        'shelf_life',
        'processing_type',
        'flavor',
        'featured_image',
        'group_image1',
        'group_image2',
        'group_image3',
    ];

    public function sluggable(): array
    {
        return [
            'slug'=>[
               'source' =>'name_en'
            ]
        ];
    }

    public function getRouteKey()
    {
        return $this->slug;
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
}
