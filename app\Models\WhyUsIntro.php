<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class WhyUsIntro extends Model
{
    use Sluggable;

    protected $table ='reasons';

    protected $fillable = [
        'tag_en',
        'tag_am',
        'title_en',
        'title_am',
        'featured_image_caption_en',
        'featured_image_caption_am',
        'featured_image'
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
