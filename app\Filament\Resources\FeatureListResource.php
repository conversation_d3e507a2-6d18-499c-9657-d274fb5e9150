<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FeatureListResource\Pages;
use App\Filament\Resources\FeatureListResource\RelationManagers;
use App\Models\FeatureList;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FeatureListResource extends Resource
{
    protected static ?string $model = FeatureList::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('content_en')->label('Content (English)')->required(),
                Forms\Components\TextInput::make('content_am')->label('Content (Amharic)')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('content_en')->label('Content (English)'),
                Tables\Columns\TextColumn::make('content_am')->label('Content (Amharic)'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFeatureLists::route('/'),
            'create' => Pages\CreateFeatureList::route('/create'),
            'edit' => Pages\EditFeatureList::route('/{record}/edit'),
        ];
    }
}
