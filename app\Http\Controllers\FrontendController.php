<?php

namespace App\Http\Controllers;

use App\Models\AboutCompanySection;
use App\Models\AboutList;
use App\Models\AboutPageCertificate;
use App\Models\AboutPageCertificateIntro;
use App\Models\AboutPageContent;
use App\Models\AboutPageWhyContent;
use App\Models\AboutPageWhyIntro;
use App\Models\AboutSection;
use App\Models\ChooseSection;
use App\Models\CompanyGoal;
use App\Models\CompanyStory;
use App\Models\Contact;
use App\Models\ContactSection;
use App\Models\EnquireContent;
use App\Models\EnquireIntro;
use App\Models\Fact;
use App\Models\Feature;
use App\Models\FeatureIntro;
use App\Models\FeatureList;
use App\Models\Gallery;
use App\Models\GeneralFaq;
use App\Models\HeroSlide;
use App\Models\Partner;
use App\Models\ProcessCard;
use App\Models\ProcessIntro;
use App\Models\Product;
use App\Models\ProductIntro;
use App\Models\ServiceFaq;
use App\Models\ShopSection;
use App\Models\SiteIdentity;
use App\Models\Testimonial;
use App\Models\TestimonialIntro;
use Illuminate\Http\Request;
use App\Models\UserMessage;
use App\Models\WhyUsIntro;
use App\Models\WhyUsList;
use App\Models\RecentSearch;
use App\Models\Review;

class FrontendController extends Controller
{
    public function StoreUserMessage(Request $request){

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        $message = new UserMessage();
        $message->name = $request->name;
        $message->email = $request->email;
        $message->phone = $request->phone;
        $message->subject = $request->subject;
        $message->message = $request->message;
        $message->save();

        return redirect()->back()->with('success', 'Message sent successfully.');
    }

public function ShowChooseCard(){
    $choose_card = ChooseSection::all();
    return $choose_card;
}

public function getHeroSlide(){
    $slides = HeroSlide::all();
    return $slides;
}

public function getAboutSection(){
    $about_section = AboutSection::first();
    return $about_section;
}

public function getAboutList(){
    $about_list = AboutList::all();
    return $about_list;

}

public function getFact(){
    $facts = Fact::all();
    return $facts;
}

public function getAboutCompany(){
   $about_company = AboutCompanySection::first();
    return $about_company;

}

public function getCompanyStory(){
   $story = CompanyStory::first();
    return $story;
}

public function getCompanyGoal(){
    $goal= CompanyGoal::first();
    return $goal;
}

public function getShope(){
    $shop = ShopSection::first();
    return $shop;
}

public function getProductIntro(){
    $product_intro = ProductIntro::first();
    return $product_intro;
}

public function getWhyIntro(){
    $why_intro = WhyUsIntro::first();
    return $why_intro;
}

public function getWhyList(){
    $why_list = WhyUsList::all();
    return $why_list;
}

public function getFeatureIntro(){
    $feature_intro = FeatureIntro::first();
    return $feature_intro;
}

public function getFeatureList(){
    $feature_lists = FeatureList::all();
    return $feature_lists;
}

public function getFeature(){
    $features = Feature::all();
    return $features;
}

public function getProcessIntro(){
    $process_intro = ProcessIntro::first();
    return $process_intro;
}

public function getProcessCard(){
    $process_cards = ProcessCard::all();
    return $process_cards;
}

public function getContactSection(){
    $contact_section = ContactSection::first();
    return $contact_section;
}

public function getTestimonialIntro(){
    $testimonial_intro = TestimonialIntro::first();
    return $testimonial_intro;
}

public function getTestimonial(){
    $testimonials= Testimonial::all();
    return $testimonials;
}

public function getPartner(){
    $partners= Partner::all();
    return $partners;
}

public function getCertificateIntro(){
    $certificate_intro = AboutPageCertificateIntro::first();
    return $certificate_intro;
}

public function getCertificate(){
    $certificates = AboutPageCertificate::all();
    return $certificates;
}

public function getAboutPageWhyIntro(){
    $why_intro = AboutPageWhyIntro::first();
    return $why_intro;

}

public function getAboutPageWhyContent(){
    $why_contents = AboutPageWhyContent::all();
    return $why_contents;

}

public function getAboutPageContent(){
    $about_content = AboutPageContent::first();
    return $about_content;
}

public function getGalleries(){
    $images = Gallery::with('category')->get();
    return $images;
}

public function getGeneralFaq(){

    $general_faq = GeneralFaq::all();
    return $general_faq;
}

public function getServiceFaq(){
    $service_faq = ServiceFaq::all();
    return $service_faq;
}

public function getEnquireIntro(){
    $enquire_intro = EnquireIntro::first();
    return $enquire_intro;
}

public function getEnquireContent(){
    $enquire_content = EnquireContent::all();
    return $enquire_content;
}

public function getContact(){
    $contact = Contact::first();
    return $contact;
}

public function getSiteIdentity(){
    $site_id = SiteIdentity::first();
    return $site_id;
}

public function getProduct(Request $request){
    // $products = Product::paginate(2);
    // return view('product.index', compact('products'));
    // $products = Product::all();
    $perPage = 2;
    $page = $request->get('page', 1);

    $products = Product::skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

    $total = Product::count();
    $totalPages = ceil($total / $perPage);
    return [
        'products' => $products,
        'page' => $page,
        'totalPages' => $totalPages,
    ];
}

public function getProductHome(){
    $products = Product::all();
    return $products;
}

public function productDetail(Product $product){
    $product->load('reviews'); // Load reviews relationship
    $site_id = SiteIdentity::first();


    return view('frontend/product-details', compact('product', 'site_id'));
}

public function getRecentSearches(){
    return RecentSearch::getRecentKeywords(5); // Get top 5 recent searches
}

public function storeReview(Request $request)
{
    $request->validate([
        'product_id' => 'required|exists:products,id',
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'review' => 'required|string|max:1000',
    ]);

    Review::create([
        'product_id' => $request->product_id,
        'name' => $request->name,
        'email' => $request->email,
        'review' => $request->review,
    ]);

    return redirect()->back()->with('success', 'Thank you for your review! It has been submitted successfully.');
}

}
