.single-contact-info-box{
    position: relative;
    display: block;
    background: #ffffff;
    text-align: center;
    padding: 40px 0 36px;
    border-radius: 10px;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.15);
    margin-bottom: 30px;
    z-index: 2;
}
.single-contact-info-box .icon{
    position: relative;
    display: block;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border: 1px solid #dae5ec;
    border-radius: 50%;
    padding: 11px;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.single-contact-info-box:hover .icon{
    border-color: var(--thm-primary);
}
.single-contact-info-box .icon span:before {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    background: #ecf2f6;
    border-radius: 50%;
    color: var(--thm-primary);
    font-size: 25px;
    line-height: 56px;
    text-align: center;
    z-index: 1;
    transition: all 200ms linear;
    transition-delay: 0.1s;    
}
.single-contact-info-box:hover .icon span:before{
    color: #ffffff;
    background: var(--thm-base);
}
.single-contact-info-box .icon span:after {
    position: absolute;
    top: -1px;
    left: -1px;
    bottom: -1px;
    right: -1px;
    content: "";
    border-radius: 50%;
    z-index: -1;
    transform: scale(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    background: var(--thm-primary);
}
.single-contact-info-box:hover .icon span:after{
    transform: scale(1.0);
}
.single-contact-info-box .text{
    position: relative;
    display: block;
    padding-top: 27px;
}
.single-contact-info-box .text h3{
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 4px;
}
.single-contact-info-box .text p{
    margin: 0;
}
.single-contact-info-box .text p a{
    color: #585858;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.single-contact-info-box .text p a:hover{
    color: var(--thm-base);
}






/*** 
=============================================
    Contact Form Area Css
=============================================
***/
.main-contact-form-area {
    position: relative;
    display: block;
    background: #ffffff;
    padding: 110px 0 110px;
    z-index: 10;
}

.contact-style1_form {
    position: relative;
    display: block;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    padding: 50px 50px 50px;
}
.contact-style1_form .top-title{
    position: relative;
    display: block;
    margin-top: -3px;
    padding-bottom: 21px;
}
.contact-style1_form .top-title h3{
    font-size: 30px;
    line-height: 36px;
    font-weight: 700;
    text-transform: capitalize;
}

.contact-form {
    position: relative;
    display: block;
}
.contact-form form{
    position: relative;
    display: block;
}
.contact-form form .input-box{
    position: relative;
    display: block;
    margin-bottom: 15px;
}
.contact-form form .input-box .icon {
    position: absolute;
    top: 0;
    left: 20px;
    bottom: 0;
    color: var(--thm-primary);
    font-size: 18px;
    line-height: 53px;
    width: 30px;
    z-index: 2;
}
.contact-form form .input-box .icon:after {
    position: absolute;
    top: 15px;
    right: 0;
    bottom: 15px;
    width: 1px;
    background: #d1d9dd;
    content: "";
}

.contact-form form .input-box.two .icon:after {
    top: 15px;
    right: 0;
    bottom: auto;
    width: 1px;
    height: 24px;
}

.contact-form form input[type="text"],
.contact-form form input[type="email"],
.contact-form form textarea{
    position: relative;
    display: block;   
    background: #ecf2f6;
    width: 100%;
    height: 55px;
    border: 1px solid #ecf2f6;
    color: #585858;
    font-size: 16px;
    font-weight: 400;
    font-style: normal;
    padding-left: 65px;
    padding-right: 30px;
    border-radius: 27px;
    transition: all 500ms ease;
    font-family: var(--thm-font);
}
.contact-form form textarea {
    height: 100px;
    padding-top: 12px;
    padding-left: 65px;
    border-radius: 27px;
}

.contact-form form input[type="text"]::-webkit-input-placeholder {
    color: #585858;
}
.contact-form form input[type="text"]:-moz-placeholder {
    color: #585858;
}
.contact-form form input[type="text"]::-moz-placeholder {
    color: #585858;
}
.contact-form form input[type="text"]:-ms-input-placeholder {
    color: #585858;
}
.contact-form form input[type="email"]::-webkit-input-placeholder {
    color: #585858;
}
.contact-form form input[type="email"]:-moz-placeholder {
    color: #585858;
}
.contact-form form input[type="email"]::-moz-placeholder {
    color: #585858;
}
.contact-form form input[type="email"]:-ms-input-placeholder {
    color: #585858;
}
.contact-form form textarea::-webkit-input-placeholder {
    color: #585858;
}
.contact-form form textarea:-moz-placeholder {
    color: #585858;
}
.contact-form form textarea::-moz-placeholder {
    color: #585858;
}
.contact-form form textarea:-ms-input-placeholder {
    color: #585858;
}
.contact-form form .button-box {
    position: relative;
    padding-top: 5px;
}
.contact-form form .button-box button{
    position: relative;
    display: block;
}



.our-service-box{
    position: relative;
    display: block;
}
.single-service{
    position: relative;
    display: block;
}
.single-service .img-holder{
    position: relative;
    display: block;
    overflow: hidden;
}
.single-service .img-holder img{
    width: 100%;
}

.single-service .text-holder{
    position: relative;
    display: block;
    padding: 37px 30px 40px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}
.single-service .text-holder .title{
    position: relative;
    display: block;
    line-height: 0;
    padding-bottom: 22px;
}
.single-service .text-holder .title h3{
    color: #151515;
    font-size: 24px;
    line-height: 30px;
    font-weight: 600;
}
.single-service .text-holder .title .decor{
    position: relative;
    display: inline-block;
    margin-top: 12px;
    line-height: 0;    
}

.single-service .text-holder p{
    margin: 0;
}
.single-service .text-holder .btns-box{
    position: relative;
    display: block;
    padding-top: 22px;
}











/*** 
=============================================
    Google Map Area Css
=============================================
***/
.google-map-area{
    position: relative;
    display: block;
    padding-bottom: 110px;
}
.google-map-area .container-fluid{
    padding: 0;
}
.contact-page-map-outer{
    position: relative;
    display: block;
    z-index: 10;
}
.contact-page-map-outer .map-canvas{
	position: relative;
	width: 100%;
	height: 500px;    
}









