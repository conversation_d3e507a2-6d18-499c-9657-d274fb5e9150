<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TestimonialResource\Pages;
use App\Filament\Resources\TestimonialResource\RelationManagers;
use App\Models\Testimonial;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TestimonialResource extends Resource
{
    protected static ?string $model = Testimonial::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title_en')->label('Title (English)')->required(),
                Forms\Components\TextInput::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\Textarea::make('content_en')->label('Content (English)')->required(),
                Forms\Components\Textarea::make('content_am')->label('Content (Amharic)')->required(),
                Forms\Components\TextInput::make('name_en')->label('Name (English)')->required(),
                Forms\Components\TextInput::make('name_am')->label('Name (Amharic)')->required(),
                Forms\Components\TextInput::make('address_en')->label('Address (English)')->required(),
                Forms\Components\TextInput::make('address_am')->label('Address (Amharic)')->required(),
                Forms\Components\FileUpload::make('profile')->label('Profile')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_en')->label('Content (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_am')->label('Content (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_am')->label('Name (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('address_en')->label('Address (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('address_am')->label('Address (Amharic)')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('profile')->label('Profile'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTestimonials::route('/'),
            'create' => Pages\CreateTestimonial::route('/create'),
            'edit' => Pages\EditTestimonial::route('/{record}/edit'),
        ];
    }
}
