<?php

namespace App\Filament\Resources\WhyUsListResource\Pages;

use App\Filament\Resources\WhyUsListResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListWhyUsLists extends ListRecords
{
    protected static string $resource = WhyUsListResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
