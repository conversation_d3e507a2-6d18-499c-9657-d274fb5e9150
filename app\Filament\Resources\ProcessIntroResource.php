<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProcessIntroResource\Pages;
use App\Filament\Resources\ProcessIntroResource\RelationManagers;
use App\Models\ProcessIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProcessIntroResource extends Resource
{
    protected static ?string $model = ProcessIntro::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')->label('Tag (English)')->required(),
                Forms\Components\TextInput::make('tag_am')->label('Tag (Amharic)')->required(),
                Forms\Components\RichEditor::make('title_en')->label('Title (English)')->required(),
                Forms\Components\RichEditor::make('title_am')->label('Title (Amharic)')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProcessIntros::route('/'),
            'create' => Pages\CreateProcessIntro::route('/create'),
            'edit' => Pages\EditProcessIntro::route('/{record}/edit'),
        ];
    }
}
