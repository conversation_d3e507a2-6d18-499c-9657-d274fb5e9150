<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('general_faqs', function (Blueprint $table) {
            $table->id();
            $table->text('question_en');
            $table->text('question_am');
            $table->text('answer_en');
            $table->text('answer_am');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('general_faqs');
    }
};
