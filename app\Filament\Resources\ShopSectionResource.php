<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShopSectionResource\Pages;
use App\Filament\Resources\ShopSectionResource\RelationManagers;
use App\Models\ShopSection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShopSectionResource extends Resource
{
    protected static ?string $model = ShopSection::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('water_mark_en')->label('Water Mark En'),
                Forms\Components\TextInput::make('water_mark_am')->label('Water Mark Am'),
                Forms\Components\FileUpload::make('feature_image1')->label('Feature Image1'),
                Forms\Components\FileUpload::make('feature_image2')->label('Feature Image2'),
                Forms\Components\RichEditor::make('order_caption_en')->label('Order Caption En'),
                Forms\Components\RichEditor::make('order_caption_am')->label('Order Caption Am'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('water_mark_en')->label('Background Water Mark En'),
                Tables\Columns\TextColumn::make('water_mark_am')->label('Background Water Mark Am'),
                Tables\Columns\ImageColumn::make('feature_image1')->label('Featured Image-1'),
                Tables\Columns\ImageColumn::make('feature_image2')->label('Featured Image-2'),
                Tables\Columns\TextColumn::make('order_caption_en')->label('Order Caption English'),
                Tables\Columns\TextColumn::make('order_caption_am')->label('Order Caption Amharic'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShopSections::route('/'),
            'create' => Pages\CreateShopSection::route('/create'),
            'edit' => Pages\EditShopSection::route('/{record}/edit'),
        ];
    }
}
