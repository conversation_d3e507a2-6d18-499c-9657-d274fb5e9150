<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutSectionResource\Pages;
use App\Filament\Resources\AboutSectionResource\RelationManagers;
use App\Models\AboutSection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AboutSectionResource extends Resource
{
    protected static ?string $model = AboutSection::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tagline_en')
                    ->label('Tagline_en')
                    ->required(),
                Forms\Components\TextInput::make('tagline_am')
                    ->label('Tagline_am')
                    ->required(),
                Forms\Components\RichEditor::make('title_en')
                    ->label('Title_English')
                    ->required(),
                Forms\Components\RichEditor::make('title_am')
                    ->label('Title_Amharic')
                    ->required(),
                Forms\Components\RichEditor::make('subtitle_en')
                    ->label('Subtitle English')
                    ->required(),
                Forms\Components\RichEditor::make('subtitle_am')
                    ->label('Subtitle Amharic')
                    ->required(),
                Forms\Components\RichEditor::make('content_en')
                    ->label('Content English')
                    ->required(),
                Forms\Components\RichEditor::make('content_am')
                    ->label('Content Amharic')
                    ->required(),
                Forms\Components\FileUpload::make('featured_image')
                    ->label('Featured Image')
                    ->required(),
                Forms\Components\RichEditor::make('featured_image_caption_en')
                    ->label('Featured Image Caption English')
                    ->required(),
                Forms\Components\RichEditor::make('featured_image_caption_am')
                    ->label('Featured Image Caption Amharic')
                    ->required(),
                Forms\Components\FileUpload::make('certificate_image')
                    ->label('Certificate Image')
                    ->required(),
                Forms\Components\RichEditor::make('certificate_image_caption_en')
                    ->label('Certificate Image Caption English')
                    ->required(),
                Forms\Components\RichEditor::make('certificate_image_caption_am')
                    ->label('Certificate Image Caption Amharic')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tagline_en')->label('Tag English')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('tagline_am')->label('Tag Amharic')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('title_en')->label('Title English')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title Amharic')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('subtitle_en')->label('Subtitle English')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('subtitle_am')->label('Title Amharic')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('content_en')->label('Content English')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('content_am')->label('Content Amharic')->searchable()->sortable(),
                Tables\Columns\ImageColumn::make('featured_image')->label('Featured Image'),
                Tables\Columns\TextColumn::make('featured_image_caption_en')->label('Featured Image Caption English')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('featured_image_caption_am')->label('Featured Image Caption Amharic')->searchable()->sortable(),
                Tables\Columns\ImageColumn::make('certificate_image')->label('Certificate Image'),
                Tables\Columns\TextColumn::make('certificate_image_caption_en')->label('Certificate Image Caption English')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('certificate_image_caption_am')->label('Certificate Image Caption Amharic')->searchable()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutSections::route('/'),
            'create' => Pages\CreateAboutSection::route('/create'),
            'edit' => Pages\EditAboutSection::route('/{record}/edit'),
        ];
    }
}
