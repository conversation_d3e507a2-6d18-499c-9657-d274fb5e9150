

/*** 
=============================================
    Footer area style
=============================================
***/
.footer-area {
    position: relative;
    display: block;
    z-index: 10;
}
.footer {
    position: relative;
    display: block;
    padding: 110px 0 93px;
    z-index: 1;
    background: #004da1;
}
.single-footer-widget {
    position: relative;
    display: block;
}
.single-footer-widget.martop{
    margin-top: 3px;
}

.single-footer-widget .title {
    position: relative;
    display: block;
    line-height: 0;
    margin-top: -1px;
    margin-bottom: 27px;
}
.single-footer-widget .title h3{
    color: #ffffff;
    font-size: 24px;
    line-height: 26px;
    font-weight: 600;
    text-transform: capitalize;
    margin: 0 0 14px;
}
.single-footer-widget .title .decor{
    position: relative;
    display: inline-block;
    line-height: 0;
}


.single-footer-widget .our-company-info {
    position: relative;
    display: block;
}
.single-footer-widget .our-company-info .footer-logo{
    position: relative;
    display: block;
    margin-bottom: 32px;
}
.single-footer-widget .our-company-info .footer-logo a{
    position: relative;
    display: inline-block;
}
.single-footer-widget .our-company-info .text-box {
    position: relative;
    display: block;
}
.single-footer-widget .our-company-info .text-box p{
    color: #ccdbeb;
    font-size: 16px;
    line-height: 26px;
    margin: 0;
}
.our-company-info-list{
    position: relative;
    display: block;
    margin-top: 22px;
}
.our-company-info-list ul{
    position: relative;
    display: block;
    overflow: hidden;
}
.our-company-info-list ul li{
    position: relative;
    display: block;
    color: #ccdbeb;
    font-size: 16px;
    line-height: 26px;
    font-weight: 400;
    margin-bottom: 7px;
}
.our-company-info-list ul li:last-child{
    margin-bottom: 0;
}
.our-company-info-list ul li span::before{
    position: relative;
    display: inline-block;
    color: var(--thm-primary);
    padding-right: 15px;
}



.footer-widget-links{
    position: relative;
    display: block;
    overflow: hidden;
}
.footer-widget-links ul{
    position: relative;
    display: block;
    overflow: hidden;
}
.footer-widget-links ul.marleft-60{
    margin-left: 50px;
}
.footer-widget-links ul li{
    position: relative;
    display: block;
    line-height: 36px;
}
.footer-widget-links ul li a{
    color: #ccdbeb;
    font-size: 16px;
    font-weight: 400;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.footer-widget-links ul li a:hover{
    color: #ffffff;
}


.single-footer-widget-Location{
    position: relative;
    display: block;
    padding-top: 8px;
}
.single-footer-widget-Location h4{
    color: #ffffff;
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
    margin-bottom: 13px;
}
.single-footer-widget-Location p{
    color: #ccdbeb;
    margin: 0;
    margin-bottom: 16px;
}
.single-footer-widget-Location .phone{
    position: relative;
    display: block;
}
.single-footer-widget-Location .phone a{
    color: #ccdbeb;
}
.single-footer-widget-Location .email{
    position: relative;
    display: block;
}
.single-footer-widget-Location .email a{
    color: #ccdbeb;
}
.location_carousel .owl-nav{
    position: relative;
    display: block; 
    padding-top: 20px;   
}


.single-footer-widget-business-hours {
    position: relative;
    display: block;
    padding-top: 5px;
}
.single-footer-widget-business-hours ul{
    position: relative;
    display: block;
    overflow: hidden;
}
.single-footer-widget-business-hours ul li{
    position: relative;
    display: block;
    color: #ccdbeb;
    margin-bottom: 16px;
}
.single-footer-widget-business-hours ul li:last-child{
    margin-bottom: 0;
}
.single-footer-widget-business-hours .btn-box{
    position: relative;
    display: block;
    line-height: 0;
    padding-top: 29px;
}
.single-footer-widget-business-hours .btn-box a:after{
    background: var(--thm-primary);
}
.single-footer-widget-business-hours .btn-box a.btn-one .round{
    background: #5dbcdf;
}
.single-footer-widget-business-hours .btn-box a.btn-one:before {
    display: none;
}


.footer-bottom{
    position: relative;
    display: block;
    background: #02458d;
    padding: 22px 0px 24px;
}
.footer-bottom .bottom-inner{
    position: relative;
    display: block;
}
.footer-bottom .bottom-inner .copyright{
    position: relative;
    display: block;
}
.footer-bottom .bottom-inner .copyright p{
    color: #ccdbeb;
    margin: 0;
}
.footer-bottom .bottom-inner .copyright p a{
    color: var(--thm-primary);
}

.footer-bottom .bottom-inner .footer-nav{
    position: relative;
    display: block;
    overflow: hidden;
}
.footer-bottom .bottom-inner .footer-nav li{
    position: relative;
    display: inline-block;
    float: left;
    padding-right: 18px;
    margin-right: 10px;
}
.footer-bottom .bottom-inner .footer-nav li:before{
    content: "";
    position: absolute;
    right: 0;
    bottom: 6px;
    width: 8px;
    height: 1px;
    background: #ffffff;
}
.footer-bottom .bottom-inner .footer-nav li:last-child:before{
    display: none;
}
.footer-bottom .bottom-inner .footer-nav li:last-child{
    margin-right: 0;
    padding-right: 0;
}
.footer-bottom .bottom-inner .footer-nav li a{
    color: #ccdbeb;
    font-size: 16px;
    line-height: 26px;
    font-weight: 400;
}
.footer-bottom .bottom-inner .footer-nav li a:hover{
    color: #ffffff;
}



/*** 
=============================================
    Footer Style2 Area style
=============================================
***/
.footer-style2-area{
    position: relative;
    display: block;
}
.footer-style2{
    position: relative;
    display: block;
}
.footer-style2 .auto-container{
    max-width: 1360px;
}
.footer-style2 .outer-box{
    position: relative;
    display: block;
    background: #ecf2f6;
    padding: 80px 80px 60px;
    border-radius: 10px;
    z-index: 10;
    margin-top: -200px;
}

.single-footer-widget-style2{
    position: relative;
    display: block;
}
.single-footer-widget-style2 .border-left{
    position: absolute;
    top: 1px;
    left: -30px;
    height: 170px;
    width: 1px;
    background: #d1d9dd;
}


.single-footer-widget-style2 .our-company-info {
    padding-right: 50px;
}
.single-footer-widget-style2 .our-company-info .text-box p {
    color: #585858;
}
.single-footer-widget-style2 .title h3 {
    color: #151515;
}

.single-footer-widget-style2 .footer-widget-links ul li a {
    color: #585858;
}

.single-footer-widget-online-book {
    position: relative;
    display: block;
    padding-top: 5px;
}
.single-footer-widget-online-book p{
    margin: 0;
}
.single-footer-widget-online-book .btns-box{
    position: relative;
    display: block;
    padding-top: 14px;
}


.order-box {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    border-top: 1px solid #d1d9dd;
    padding-top: 50px;
    margin-top: 48px;
}
.order-box .title-holder{
    position: relative;
    display: flex;
    align-items: center;
}
.order-box .title-holder .icon{
    position: relative;
    width: 80px;
    height: 80px;
}
.order-box .title-holder .icon .icon-bg{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
}
.order-box .title-holder .icon span:before{
    position: relative;
    display: block;
    color: #ffffff;
    font-size: 40px;
    line-height: 80px;
    text-align: center;
}

.order-box .title-holder .text{
    position: relative;
    display: block;
    padding-left: 30px;
}
.order-box .title-holder .text h2{
    font-size: 30px;
    line-height: 40px;
    font-weight: 600;
    margin: 0 0 6px;
}
.order-box .title-holder .text p{
    margin: 0;
}
.order-box .btns-box{
    position: relative;
    display: block;
}
.order-box .btns-box a {
    line-height: 60px;
}
.order-box .btns-box a.btn-one:before {
    background: var(--thm-primary);
}
.order-box .btns-box a.btn-one:after {
    background: var(--thm-primary);
}
.order-box .btns-box a.btn-one .round {
    background: #5dbcdf;
    height: 60px;
}


.footer-bottom--style2{
    background: #ffffff;
}
.footer-bottom--style2 .bottom-inner .copyright p {
    color: #585858;
}
.footer-bottom--style2 .bottom-inner .footer-nav li a {
    color: #585858;
}
.footer-bottom--style2 .bottom-inner .footer-nav li a:hover {
    color: var(--thm-primary);
}
.footer-bottom--style2 .bottom-inner .footer-nav li:before {
    background: #585858;
}