<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name_en');
            $table->string('name_am');
            $table->string('tip_en');
            $table->string('tip_am');
            $table->string('description_en');
            $table->string('description_am');
            $table->string('packaging_size');
            $table->string('packaging_type');
            $table->string('shelf_life');
            $table->string('processing_type');
            $table->string('flavor');
            $table->string('featured_image');
            $table->string('group_image1');
            $table->string('group_image2');
            $table->string('group_image3');
            $table->string('slug');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
