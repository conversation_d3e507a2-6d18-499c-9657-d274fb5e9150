<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FeatureIntroResource\Pages;
use App\Filament\Resources\FeatureIntroResource\RelationManagers;
use App\Models\FeatureIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FeatureIntroResource extends Resource
{
    protected static ?string $model = FeatureIntro::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('tag_en')->label('Tag (English)')->required(),
                Forms\Components\TextInput::make('tag_am')->label('Tag (Amharic)')->required(),
                Forms\Components\RichEditor::make('title_en')->label('Title (English)')->required(),
                Forms\Components\RichEditor::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\TextInput::make('intro_en')->label('Intro (English)')->required(),
                Forms\Components\TextInput::make('intro_am')->label('Intro (Amharic)')->required(),
                Forms\Components\FileUpload::make('bg_image')->label('Background Image'),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('intro_en')->label('Intro (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('intro_am')->label('Intro (Amharic)')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('bg_image')->label('Background Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFeatureIntros::route('/'),
            'create' => Pages\CreateFeatureIntro::route('/create'),
            'edit' => Pages\EditFeatureIntro::route('/{record}/edit'),
        ];
    }
}
