<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EnquireContentResource\Pages;
use App\Filament\Resources\EnquireContentResource\RelationManagers;
use App\Models\EnquireContent;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EnquireContentResource extends Resource
{
    protected static ?string $model = EnquireContent::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title_en')
                    ->label('Title En')
                    ->required(),
                Forms\Components\TextInput::make('title_am')
                    ->label('Title Am')
                    ->required(),
                Forms\Components\Textarea::make('content_en')
                    ->label('Content En')
                    ->required(),
                Forms\Components\Textarea::make('content_am')
                    ->label('Content Am')
                    ->required(),
                Forms\Components\FileUpload::make('image')
                    ->label('Image')
                    ->image(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_en')->label('Title English')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title Amharic')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content_en')->label('Content English')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content_am')->label('Content Amharic')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('image')
                    ->label('Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEnquireContents::route('/'),
            'create' => Pages\CreateEnquireContent::route('/create'),
            'edit' => Pages\EditEnquireContent::route('/{record}/edit'),
        ];
    }
}
