<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutPageContentResource\Pages;
use App\Filament\Resources\AboutPageContentResource\RelationManagers;
use App\Models\AboutPageContent;
use Filament\Forms;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AboutPageContentResource extends Resource
{
    protected static ?string $model = AboutPageContent::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                        Forms\Components\FileUpload::make('featured_image')
                            ->image(),


                        Forms\Components\TextInput::make('tag_en')
                            ->label('Tag (EN)')
                            ->required(),
                        Forms\Components\TextInput::make('tag_am')
                            ->label('Tag (AM)')
                            ->required(),

                        Forms\Components\RichEditor::make('title_en')
                            ->label('Title (EN)')
                            ->required(),
                        Forms\Components\RichEditor::make('title_am')
                            ->label('Title (AM)')
                            ->required(),

                        Forms\Components\RichEditor::make('sub_title_en')
                            ->label('Sub Title (EN)')
                            ->required(),
                        Forms\Components\RichEditor::make('sub_title_am')
                            ->label('Sub Title (AM)')
                            ->required(),

                        Forms\Components\Textarea::make('description_en')
                            ->label('Description (EN)')
                            ->required(),
                        Forms\Components\Textarea::make('description_am')
                            ->label('Description (AM)')
                            ->required(),

                        Forms\Components\TextInput::make('story_title_en')
                            ->label('Story Title (EN)')
                            ->required(),
                        Forms\Components\TextInput::make('story_title_am')
                            ->label('Story Title (AM)')
                            ->required(),

                        Forms\Components\TextInput::make('story_tip_en')
                            ->label('Story Tip (EN)')
                            ->required(),
                        Forms\Components\TextInput::make('story_tip_am')
                            ->label('Story Tip (AM)')
                            ->required(),

                        Forms\Components\FileUpload::make('story_image')
                            ->image(),

                        Forms\Components\TextInput::make('what_we_do_title_en')
                            ->label('What We Do Title (EN)')
                            ->required(),
                        Forms\Components\TextInput::make('what_we_do_title_am')
                            ->label('What We Do Title (AM)')
                            ->required(),

                        Forms\Components\Textarea::make('what_we_do_en')
                            ->label('What We Do (EN)')
                            ->required(),
                        Forms\Components\Textarea::make('what_we_do_am')
                            ->label('What We Do (AM)')
                            ->required(),

                        Forms\Components\FileUpload::make('what_we_do_image')
                            ->image(),

                        Forms\Components\TextInput::make('general_manager_name_en')
                            ->label('General Manager Name (EN)')
                            ->required(),
                        Forms\Components\TextInput::make('general_manager_name_am')
                            ->label('General Manager Name (AM)')
                            ->required(),

                        Forms\Components\FileUpload::make('general_manager_signature_image')
                            ->image(),
                ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('featured_image')
                    ->label('Featured Image'),
                Tables\Columns\TextColumn::make('tag_en')
                    ->label('Tag (EN)'),
                Tables\Columns\TextColumn::make('tag_am')
                    ->label('Tag (AM)'),
                Tables\Columns\TextColumn::make('title_en')->label('Title (EN)'),
                Tables\Columns\TextColumn::make('title_am')->label('Title (AM)'),
                Tables\Columns\TextColumn::make('sub_title_en')->label('Sub Title (EN)'),
                Tables\Columns\TextColumn::make('sub_title_am')->label('Sub Title (AM)'),
                Tables\Columns\TextColumn::make('description_en')->label('Description (EN)'),
                Tables\Columns\TextColumn::make('description_am')->label('Description (AM)'),
                Tables\Columns\TextColumn::make('story_title_en')->label('Story Title (EN)'),
                Tables\Columns\TextColumn::make('story_title_am')->label('Story Title (AM)'),
                Tables\Columns\TextColumn::make('story_tip_en')->label('Story Tip (EN)'),
                Tables\Columns\TextColumn::make('story_tip_am')->label('Story Tip (AM)'),
                Tables\Columns\ImageColumn::make('story_image')->label('Story Image'),
                Tables\Columns\TextColumn::make('what_we_do_title_en')->label('What We Do Title (EN)'),
                Tables\Columns\TextColumn::make('what_we_do_title_am')->label('What We Do Title (AM)'),
                Tables\Columns\TextColumn::make('what_we_do_en')->label('What We Do (EN)'),
                Tables\Columns\TextColumn::make('what_we_do_am')->label('What We Do (AM)'),
                Tables\Columns\ImageColumn::make('what_we_do_image')->label('What We Do Image'),
                Tables\Columns\TextColumn::make('general_manager_name_en')->label('General Manager Name (EN)'),
                Tables\Columns\TextColumn::make('general_manager_name_am')->label('General Manager Name (AM)'),
                Tables\Columns\ImageColumn::make('general_manager_signature_image')->label('General Manager Signature Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutPageContents::route('/'),
            'create' => Pages\CreateAboutPageContent::route('/create'),
            'edit' => Pages\EditAboutPageContent::route('/{record}/edit'),
        ];
    }
}
