<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('about_page_contents', function (Blueprint $table) {
            $table->text('what_we_do_detail_en')->nullable()->after('what_we_do_am');
            $table->text('what_we_do_detail_am')->nullable()->after('what_we_do_detail_en');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('about_page_contents', function (Blueprint $table) {
            $table->dropColumn(['what_we_do_detail_en', 'what_we_do_detail_am']);
        });
    }
};
