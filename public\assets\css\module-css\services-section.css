/*** 
=============================================
    service Style1 Area Css
=============================================
***/
.service-style1-area{
    position: relative;
    display: block;
    background: #ffffff;
    padding: 110px 0 110px;
    overflow: hidden;
    z-index: 1;
}
.service-style1_pattern-bg{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    min-height: 496px;
    background-attachment: scroll;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: cover;
    z-index: -1;
}

.single-service-style1{
    position: relative;
    display: block;
    max-width: 370px;
    width: 100%;
    margin: 0 auto 30px;
    background: #ffffff;
    box-shadow: 0px 0px 30px 0px #0000001a;
    padding: 40px 40px 32px;
    border-radius: 10px;
}
.single-service-style1__bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 190px;
    background-attachment: scroll;
    background-size: auto;
    background-repeat: no-repeat;
    background-position: bottom center;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
.single-service-style1 .img-holder{
    position: relative;
    display: block;
    width: 230px;
    height: 230px;
    padding: 10px;
    margin: 0 auto;
    border-radius: 50%;
    z-index: 1;
}
.single-service-style1 .img-holder::before{
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    border: 11px solid#ffffff;
    border-radius: 50%;
    content: "";
    z-index: -1;
}
.single-service-style1 .img-holder .inner {
    position: relative;
    display: block;
    border-radius: 50%;
    overflow: hidden;
    z-index: 2;
}
.single-service-style1 .img-holder img{
    width: 100%;
    border-radius: 50%;
    -webkit-transition: all 0.5s ease 0s;
    -o-transition: all 0.5s ease 0s;
    transition: all 0.5s ease 0s;
}
.single-service-style1:hover .img-holder img{

}
.single-service-style1 .img-holder .overlay-icon {
    position: absolute;
    left: 0;
    bottom: -17px;
    right: 0;
    z-index: 3;
}
.single-service-style1 .img-holder .overlay-icon a{
    position: relative;
    display: inline-block;
    width: 55px;
    height: 55px;
    line-height: 55px;
    border-radius: 50%;
    background: #ffffff;
    text-align: center;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    font-size: 30px;
    z-index: 1;
}
.single-service-style1 .img-holder .overlay-icon a::before{
    position: absolute;
    top: -10px;
    left: -10px;
    bottom: -10px;
    right: -10px;
    border: 1px solid #dae3e9;
    border-radius: 50%;
    content: "";
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.single-service-style1:hover .img-holder .overlay-icon a::before{
    border-color: var(--thm-primary);
}
.single-service-style1 .img-holder .overlay-icon a::after{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    content: "";
    border-radius: 50%;
    z-index: -1;
    transform: scale(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    background: var(--thm-primary);
}
.single-service-style1:hover .img-holder .overlay-icon a::after{
    transform: scaleX(1.0);   
}
.single-service-style1 .img-holder .overlay-icon a span::before{
    color: var(--thm-primary);
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.single-service-style1:hover .img-holder .overlay-icon a span::before{
    color: #ffffff;
}


.single-service-style1 .title-holder {
    position: relative;
    display: block;
    margin-top: 47px;
}
.single-service-style1 .title-holder h3{
    font-size: 24px;
    line-height: 34px;
    font-weight: 600;
    margin-bottom: 7px;
}
.single-service-style1 .title-holder h3 a{
    color: var(--thm-black);
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.single-service-style1 .title-holder h3 a:hover{
    color: var(--thm-base);
}
.single-service-style1 .title-holder .inner-text{
    position: relative;
    display: block;
}
.single-service-style1 .title-holder .inner-text p{
    margin: 0;
}
.single-service-style1 .title-holder .readmore-button{
    position: relative;
    display: block;
    margin-top: 17px;
}
.single-service-style1 .title-holder .readmore-button a{
    color: var(--thm-black);
    font-size: 14px;
    line-height: 24px;
    font-weight: 700;
    text-transform: uppercase;
    font-family: var(--thm-font-2);
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.single-service-style1 .title-holder .readmore-button a:hover{
    color: var(--thm-primary);
}
.single-service-style1 .title-holder .readmore-button a span::before{
    position: relative;
    display: inline-block;
    padding-right: 10px;
}

.service-style1_btns-box {
    position: relative;
    display: block;
    padding-top: 20px;
    line-height: 0;
}







/*** 
=============================================
    service Style2 Area Css
=============================================
***/
.service-style2-area{
    position: relative;
    display: block;
    background: #ffffff;
    padding: 80px 0px 70px;
}
.single-service-style2{
    position: relative;
    display: block;
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #dae5ec;
    padding: 20px 20px 40px;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}
.single-service-style2 .img-holder{
    position: relative;
    display: block;
}
.single-service-style2 .img-holder .inner{
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 10px;
}
.single-service-style2 .img-holder .inner:before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: 10px;
    background: var(--thm-black);
    opacity: 0.80;
    content: "";
    transform: skew(-90deg) translateY(100%);
    transform-origin: left;
    transform-style: preserve-3d;
    transition: all 900ms ease 100ms;
    z-index: 2;
}
.single-service-style2:hover .img-holder .inner:before{
    transform: skew(0deg) translateY(0);  
}
.single-service-style2 .img-holder .inner img{
    width: 100%;
}
.single-service-style2:hover .img-holder .inner img{
    transform: scale(1.2) rotate(1deg);
}


.single-service-style2 .title-holder{
    position: relative;
    display: block;
    padding: 30px 10px 0px
}
.single-service-style2 .title-holder .top {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 22px;
}
.single-service-style2 .title-holder .top .icon {
    position: relative;
    display: block;
    width: 80px;
    height: 80px;
    text-align: center;
    z-index: 1;
}
.single-service-style2 .title-holder .top .icon .icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    z-index: -1;
}
.single-service-style2 .title-holder .top .icon span::before{
    color: #ffffff;
    font-size: 40px;
    line-height: 80px;
}
.single-service-style2 .title-holder .top .text{
    position: relative;
    display: block;
    padding-left: 20px;
    line-height: 0;
}
.single-service-style2 .title-holder .top .text p{
    color: var(--thm-primary);
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 7px;
    font-family: var(--thm-font-2);
}
.single-service-style2 .title-holder .top .text h3{
    font-size: 24px;
    line-height: 34px;
    font-weight: 600;
    margin: 0 0 10px;
}
.single-service-style2 .title-holder .top .text h3 a{
    color: var(--thm-black);
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.single-service-style2 .title-holder .top .text h3 a:hover{
    color: var(--thm-base);
}
.single-service-style2 .title-holder .top .text .decor{
    position: relative;
    display: inline-block;
    line-height: 0;
}

.single-service-style2 .title-holder .bottom{
    position: relative;
    display: block;
}
.single-service-style2 .title-holder .bottom p{
    margin: 0;
}
.single-service-style2 .title-holder .bottom .btn-box{
    position: relative;
    display: block;
    margin-top: 24px;
    line-height: 0;
}
.single-service-style2 .title-holder .bottom .btn-box a{
    padding-left: 35px;
    padding-right: 35px;
}



/*** 
=============================================
    service Page Css
=============================================
***/
.service-page-sec1{
    padding: 110px 0 80px;
}
.service-page-sec1-bg{
    position: absolute;
    top: 110px;
    right: 0;
    bottom: 0;
    width: 45%;
    background-attachment: scroll;
    background-repeat: no-repeat;
    background-position: left top;
    background-size: cover;
}


/*** 
=============================================
    Service Details Area Css
=============================================
***/
.service-details-area{
    position: relative;
    display: block;
    background: #ffffff;
    padding: 110px 0 70px;
}
.thm-sidebar-box{
    position: relative;
    display: block;
    max-width: 340px;
    width: 100%;
}

.sidebar-title{
    position: relative;
    display: block;
    line-height: 0;
    padding-bottom: 30px;
}
.sidebar-title h3{
    color: #151515;
    font-size: 24px;
    line-height: 30px;
    font-weight: 600;
}
.sidebar-title .decor{
    position: relative;
    display: block;
    margin-top: 12px;
}

.view-all-service{
    position: relative;
    display: block;
    background: #ffffff;
    border: 1px solid #dae5ec;
    border-radius: 10px;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    padding: 35px 29px 39px;
}
.view-all-service .service-pages{
    position: relative;
    display: block;
}
.view-all-service .service-pages li{
    position: relative;
    display: block;
    margin-bottom: 5px;
}
.view-all-service .service-pages li:last-child{
    margin-bottom: 0;
}
.view-all-service .service-pages li a{
    position: relative;
    display: block;
    background: #ffffff;
    padding: 0 20px;
    color: #585858;
    font-size: 18px;
    font-weight: 500;
    line-height: 55px;
    border-radius: 27px;
    font-family: var(--thm-font-2);
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    z-index: 1;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.view-all-service .service-pages li a:hover{
    color: #ffffff;
}

.view-all-service .service-pages li a:before {
    font-family: FontAwesome;
    content: "\f067";
    position: absolute;
    top: 13px;
    right: 20px;
    bottom: 13px;
    width: 30px;
    height: 30px;
    background: #ffffff;
    border: 1px solid #dae5ec;
    border-radius: 50%;
    color: #98a1a7;
    font-size: 13px;
    font-weight: 400;
    text-align: center;
    line-height: 32px;
    transform: rotate(0deg);
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.view-all-service .service-pages li:hover a:before{
    color: var(--thm-primary);
    border-color: #ffffff;
}

.view-all-service .service-pages li a:after {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    content: "";
    z-index: -1;
    opacity: 1;
    transform: perspective(400px) rotateX(-90deg);
    transform-origin: top;
    transition: all 300ms linear;
    transition-delay: 0.1s;
    background: var(--thm-primary);
    border-radius: 27px;
}
.view-all-service .service-pages li:hover a:after{
	opacity: 1;
    transform: perspective(400px) rotateX(0deg);
    transition: all 300ms linear;
    transition-delay: 0.1s;   
}


.info-ownload-box{
    position: relative;
    display: block;
    margin-top: 30px;
}
.info-ownload-box ul{
    position: relative;
    display: block;
}
.info-ownload-box ul li{
    position: relative;
    display: block;
    margin-bottom: 10px;
}
.info-ownload-box ul li:last-child{
    margin-bottom: 0;
}
.info-ownload-box ul li .inner{
    position: relative;
    display: flex;
    align-items: center;
    min-height: 110px;
    padding: 0 30px;
    background: var(--thm-base);
    border-radius: 10px;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.info-ownload-box ul li:hover .inner{
    background: var(--thm-primary);
}

.info-ownload-box ul li .inner .icon{
    width: 65px;
} 
.info-ownload-box ul li .inner .icon span:before {
    color: #ffffff;
    font-size: 45px;
    line-height: 45px;
}

.info-ownload-box ul li .inner .title{
    position: relative;
    display: block;
}
.info-ownload-box ul li .inner .title h3{
    color: #ffffff;
    font-size: 20px;
    line-height: 24px;
    font-weight: 600;
    margin: 0 0 12px;
}
.info-ownload-box ul li .inner .title h6{
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    text-transform: uppercase;
}
.info-ownload-box ul li .inner .title h6 a {
    position: relative;
    display: inline-block;
    color: #ffffff;
    padding-left: 0px;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.info-ownload-box ul li .inner .title h6 a:hover{
    padding-left: 20px;
}
.info-ownload-box ul li .inner .title h6 a span:before {
    position: absolute;
    left: 0;
    opacity: 0;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.info-ownload-box ul li .inner .title h6 a:hover span:before{
    opacity: 1;
}


.sidebar-contact-info-box{
    position: relative;
    display: block;
    padding: 38px 28px 38px;
    background: #ecf2f6;
    border: 2px solid var(--thm-primary);
    border-radius: 10px;
    margin-top: 30px;
}
.sidebar-contact-info-box .icon{
    position: relative;
    display: block;
    width: 60px;
    height: 60px;
    background: var(--thm-primary);
    border-radius: 50%;
    color: #ffffff;
    font-size: 25px;
    line-height: 60px;
    text-align: center;
}
.sidebar-contact-info-box h3{
    color: var(--thm-primary);
    font-size: 20px;
    font-weight: 600;
    margin: 27px 0 10px;
}
.sidebar-contact-info-box h2{
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 14px;
}
.sidebar-contact-info-box h2 a{
    color: #151515;
}
.sidebar-contact-info-box p{
    margin: 0;
}
.sidebar-contact-info-box .btn-box{
    position: relative;
    display: block;
    line-height: 0;
    margin-top: 24px;
}


.sidebar-single-box1{
    position: relative;
    display: block;
    overflow: hidden;
    padding: 40px 0 40px;
    border-radius: 10px;
    text-align: center;
    margin-top: 30px;
    z-index: 1;
}
.sidebar-single-box1-bg{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-attachment: scroll;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    z-index: -1;
}
.sidebar-single-box1-bg:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgba(var(--thm-base-rgb), .80);
}
.sidebar-single-box1 .icon{
    position: relative;
    display: block;
    height: 90px;
    width: 90px;
    background: #ffffff;
    border-radius: 50%;
    margin: 0 auto;
    font-size: 50px;
    line-height: 90px;
}
.sidebar-single-box1 h3{
    color: #ffffff;
    font-size: 24px;
    line-height: 34px;
    font-weight: 600;
    margin: 25px 0 20px;
}
.sidebar-single-box1 .btn-box{
    position: relative;
    display: block;
    line-height: 0;
}

.sidebar-single-box1 .btn-box .btn-one{
    padding-left: 40px;
    padding-right: 40px;
}
.sidebar-single-box1 .btn-box .btn-one:after {
    background-color: var(--thm-primary);
}
.sidebar-single-box1 .btn-box .btn-one::before{
    display: none;
}
.sidebar-single-box1 .btn-box .btn-one .round {
    background: #5dbcdf;
}


.service-details-content{
    position: relative;
    display: block;
}
.service-details-content .top{
    position: relative;
    display: block;
}
.service-details-content .top h2{
    font-size: 40px;
    margin: -7px 0 28px;
}
.service-details-content .top .img-box{
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 10px;
}
.service-details-content .top .img-box img{
    width: 100%;
}


.service-details-text-box1{
    position: relative;
    display: block;
    padding-top: 37px;
    padding-bottom: 45px;
}
.service-details-text-box1 h3{
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 16px;
}


.service-details_tab{
    position: relative;
    display: block;
}
.service-details_tab .tab-buttons {
    position: relative;
    display: flex;
    align-items: center;
    padding-bottom: 60px;
    z-index: 2;
}
.service-details_tab .tab-buttons li.tab-btn{
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
    width: 25%;
    min-height: 60px;
    background: #ecf2f6;
    cursor: pointer;
    color: #98a1a7;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    font-family: var(--thm-font-2);
    transition: all 200ms linear;
    transition-delay: 0.1s;
    z-index: 1;
}
.service-details_tab .tab-buttons li.tab-btn:first-child{
    border-top-left-radius: 35px;
    border-bottom-left-radius: 35px;
}
.service-details_tab .tab-buttons li.tab-btn:last-child{
    border-top-right-radius: 35px;
    border-bottom-right-radius: 35px;
}



.service-details_tab .tab-buttons li.tab-btn::before{
    position: absolute;
    top: -5px;
    left: 0;
    bottom: -5px;
    right: 0;
    background: var(--thm-primary);
    content: "";
    border-radius: 35px;
    z-index: -1;
    transition: .5s;
    transform: perspective(400px) scaleX(0);
    -webkit-transform-origin: bottom;
    -ms-transform-origin: bottom;
    transform-origin: bottom;
}
.service-details_tab .tab-buttons li.tab-btn:hover::before,
.service-details_tab .tab-buttons li.tab-btn.active-btn::before{
    transform: perspective(400px) scaleX(1.0); 
}
.service-details_tab .tab-buttons li.tab-btn:hover,
.service-details_tab .tab-buttons li.tab-btn.active-btn{
    color: #ffffff;
}


.service-details_tab .tabs-content {
    position: relative;
    display: block;
}
.service-details_tab .tabs-content .tab{
    position: relative;
	display: none;
	-webkit-transform: translateY(5px);
	-ms-transform: translateY(5px);
	transform: translateY(5px);
	-webkit-transition:all 600ms ease;
	-moz-transition:all 600ms ease;
	-ms-transition:all 600ms ease;
	-o-transition:all 600ms ease;
	transition:all 600ms ease;
	z-index:10;
}
.service-details_tab .tabs-content .tab.active-tab{
    display: block;
	margin-top: 0px;
	-webkit-transform: translateY(0px);
	-ms-transform: translateY(0px);
	transform: translateY(0px);
}

  
.service-details-tab-content{
    position: relative;
    display: block;
    padding-left: 250px;
}
.service-details-tab-content .img-holder{
    position: absolute;
    top: 0;
    left: 0;
    width: 250px;
    height: 250px;
    padding: 20px;
    border-radius: 50%;
    z-index: 1;
}
.service-details-tab-content .img-holder .pattern-bg{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: 50%;
    background-size: cover;
    background-repeat: no-repeat;
}
.service-details-tab-content .img-holder .inner{
    position: relative;
    display: block;
    overflow: hidden;
    border-radius: 50%;
}
.service-details-tab-content .img-holder .inner img{
    width: 100%;
}

.service-details-tab-content .text-holder{
    position: relative;
    display: block;
    padding-left: 50px;
}
.service-details-tab-content .text-holder .sec-title{
    padding-bottom: 32px;
}
.service-details-tab-content .text-holder .sec-title h2{
    font-size: 24px;
    line-height: 34px;
    font-weight: 600;
}
.service-details-tab-content .text-holder .sec-title .decor {
    margin-top: 15px;
}

.service-details-tab-content .text-holder .btn-box{
    position: relative;
    display: block;
    padding-top: 7px;
}



.why-our-service{
    position: relative;
    display: block;
    padding-top: 55px;
}
.why-our-service .row {
    margin-left: -12.5px;
    margin-right: -12.5px;
}
.why-our-service .row [class*=col-] {
    padding-left: 12.5px;
    padding-right: 12.5px;
}
.why-our-service .title{
    position: relative;
    display: block;
    margin-bottom: 34px;
}
.why-our-service .title h2 {
    font-size: 30px;
    line-height: 40px;
    font-weight: 600;
    margin: 0 0 11px;
}
.why-our-service .title p{
    margin: 0;
}

.why-service-block{
    position: relative;
    display: block;
    background: #ffffff;
    border: 1px solid #dae5ec;
    border-radius: 10px;
    padding: 39px 20px 36px;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
    z-index: 1;
}
.why-service-block::before{
    position: absolute;
    content: '';
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: var(--thm-base);
    border-radius: 10px;
    transition: .5s;
    transform: perspective(400px) scaleX(0);
    -webkit-transform-origin: bottom;
    -ms-transform-origin: bottom;
    transform-origin: bottom;
    z-index: -1;
}
.why-service-block:hover:before{
    transform: perspective(400px) scaleX(1.0);
}

.why-service-block .icon-holder{
    position: relative;
    display: inline-block;
    width: 70px;
    height: 70px;
    z-index: 1;
    margin-bottom: 14px;
}
.why-service-block .icon-holder .icon-bg{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: 50%;
    z-index: -1;
}
.why-service-block .icon-holder span::before{
    color: var(--thm-primary);
    font-size: 40px;
    line-height: 70px;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.why-service-block:hover .icon-holder span::before{
    color: #ffffff;
}
.why-service-block .icon-holder .icon-bg-overlay{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: -1;
    transition: .5s;
    transform: perspective(400px) scale(0);
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}
.why-service-block:hover .icon-holder .icon-bg-overlay{
    transform: perspective(400px) scale(1.0);
}

.why-service-block .title{
    position: relative;
    display: block;
    margin-bottom: 21px;
}
.why-service-block .title h3{
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
}
.why-service-block .title h3 a{
    color: var(--thm-black);
    transition: all 200ms linear;
    transition-delay: 0.1s;
}
.why-service-block:hover .title h3 a{
    color: #ffffff;
}
.why-service-block .text{
    position: relative;
    display: block;
    border-top: 2px solid #dbe6ec;
    padding-top: 22px;
    transition: all 200ms linear;
    transition-delay: 0.3s;
}
.why-service-block:hover .text{
    border-top-color: #246fc1;
}

.why-service-block .text p{
    margin: 0;
    transition: all 200ms linear;
    transition-delay: 0.2s;
}
.why-service-block:hover .text p{
    color: #ffffff;
}

