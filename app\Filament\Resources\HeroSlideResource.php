<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HeroSlideResource\Pages;
use App\Filament\Resources\HeroSlideResource\RelationManagers;
use App\Models\HeroSlide;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HeroSlideResource extends Resource
{
    protected static ?string $model = HeroSlide::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('tag_en')->label('Tag (English)')->required(),
                Forms\Components\RichEditor::make('tag_am')->label('Tag (Amharic)')->required(),
                Forms\Components\RichEditor::make('title_en')->label('Title (English)')->required(),
                Forms\Components\RichEditor::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\RichEditor::make('content_en')->label('Content (English)')->required(),
                Forms\Components\RichEditor::make('content_am')->label('Content (English)')->required(),
                Forms\Components\FileUpload::make('featured_image')->label('Featured Image')->required(),
                Forms\Components\FileUpload::make('layer_image')->label('Layer Image')->required(),
                Forms\Components\FileUpload::make('bg_image')->label('Background')->required(),
                Forms\Components\RichEditor::make('vid_link')->label('Video Link')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tag_en')->label('Tag (English)'),
                Tables\Columns\TextColumn::make('tag_am')->label('Tag (Amharic)'),
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)'),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)'),
                Tables\Columns\TextColumn::make('content_en')->label('Content (English)'),
                Tables\Columns\TextColumn::make('content_am')->label('Content (Amharic)'),
                Tables\Columns\ImageColumn::make('featured_image')->label('Featured Image'),
                Tables\Columns\ImageColumn::make('layer_image')->label('Layer Image'),
                Tables\Columns\ImageColumn::make('bg_image')->label('Background Image'),
                Tables\Columns\TextColumn::make('vid_link')->label('Video Link'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHeroSlides::route('/'),
            'create' => Pages\CreateHeroSlide::route('/create'),
            'edit' => Pages\EditHeroSlide::route('/{record}/edit'),
        ];
    }
}
