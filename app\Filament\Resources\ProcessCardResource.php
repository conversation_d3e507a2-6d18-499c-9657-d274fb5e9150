<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProcessCardResource\Pages;
use App\Filament\Resources\ProcessCardResource\RelationManagers;
use App\Models\ProcessCard;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProcessCardResource extends Resource
{
    protected static ?string $model = ProcessCard::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title_en')->label('Title (English)')->required(),
                Forms\Components\TextInput::make('title_am')->label('Title (Amharic)')->required(),
                Forms\Components\Textarea::make('content_en')->label('Content (English)')->required(),
                Forms\Components\Textarea::make('content_am')->label('Content (Amharic)')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_en')->label('Title (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_am')->label('Title (Amharic)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_en')->label('Content (English)')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('content_am')->label('Content (Amharic)')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProcessCards::route('/'),
            'create' => Pages\CreateProcessCard::route('/create'),
            'edit' => Pages\EditProcessCard::route('/{record}/edit'),
        ];
    }
}
