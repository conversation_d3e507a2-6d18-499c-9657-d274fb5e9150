<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class Fact extends Model
{
    use Sluggable;
    protected $fillable = [
        'fact_en',
        'fact_am',
        'estimate',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'fact_en'
            ]
        ];
    }
}
