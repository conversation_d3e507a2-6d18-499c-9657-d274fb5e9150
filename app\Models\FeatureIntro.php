<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class FeatureIntro extends Model
{
    use Sluggable;
    protected $fillable = [
        'tag_en',
        'tag_am',
        'title_en',
        'title_am',
        'intro_en',
        'intro_am',
        'bg_image'
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }

}
