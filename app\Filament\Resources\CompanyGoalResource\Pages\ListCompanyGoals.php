<?php

namespace App\Filament\Resources\CompanyGoalResource\Pages;

use App\Filament\Resources\CompanyGoalResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCompanyGoals extends ListRecords
{
    protected static string $resource = CompanyGoalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
