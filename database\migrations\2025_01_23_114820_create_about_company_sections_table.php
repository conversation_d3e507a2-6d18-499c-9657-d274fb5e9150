<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_company_sections', function (Blueprint $table) {
            $table->id();
            $table->string('tag_en');
            $table->string('tag_am');
            $table->string('title_en');
            $table->string('title_am');
            $table->string('certificate_title_en');
            $table->string('certificate_title_am');
            $table->text('certificate_content_en');
            $table->text('certificate_content_am');
            $table->string('certificate_image');
            $table->string('slug');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_company_sections');
    }
};
