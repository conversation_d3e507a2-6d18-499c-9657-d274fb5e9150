<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompanyGoalResource\Pages;
use App\Filament\Resources\CompanyGoalResource\RelationManagers;
use App\Models\CompanyGoal;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CompanyGoalResource extends Resource
{
    protected static ?string $model = CompanyGoal::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('tag_en')
                    ->label('Tag (English)')
                    ->required(),
                Forms\Components\RichEditor::make('tag_am')
                    ->label('Tag (Amharic)')
                    ->required(),
                Forms\Components\RichEditor::make('title_en')
                    ->label('Title (English)')
                    ->required(),
                Forms\Components\RichEditor::make('title_am')
                    ->label('Title (Amharic)')
                    ->required(),
                Forms\Components\RichEditor::make('content_en')
                    ->label('Content (English)')
                    ->required(),
                Forms\Components\RichEditor::make('content_am')
                    ->label('Content (Amharic)')
                    ->required(),
                Forms\Components\RichEditor::make('detail_content_en')
                    ->label('Detail Content (English)'),
                Forms\Components\RichEditor::make('detail_content_am')
                    ->label('Detail Content (Amharic)'),
                Forms\Components\FileUpload::make('featured_image')
                    ->image()
                    ->label('Featured Image')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
            Tables\Columns\TextColumn::make('tag_en')
                ->label('Tag (English)'),
            Tables\Columns\TextCOlumn::make('tag_am')
                ->label('Tag (Amharic)'),
            Tables\Columns\TextColumn::make('title_en')
                ->label('Title (English)'),
            Tables\Columns\TextCOlumn::make('title_am')
                ->label('Title (Amharic)'),
            Tables\Columns\TextColumn::make('content_en')
                ->label('Content (English)'),
            Tables\Columns\TextColumn::make('content_am')
                ->label('Content (Amharic)'),
            Tables\Columns\TextColumn::make('detail_content_en')
                ->label('Detail Content (English)'),
            Tables\Columns\TextColumn::make('detail_content_am')
                ->label('Detail Content (Amharic)'),
            Tables\Columns\ImageColumn::make('featured_image')
                ->label('Featured Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanyGoals::route('/'),
            'create' => Pages\CreateCompanyGoal::route('/create'),
            'edit' => Pages\EditCompanyGoal::route('/{record}/edit'),
        ];
    }
}
