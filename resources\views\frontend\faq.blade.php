@extends('frontend.layouts.frontend')

@section('title','Faqs')

@section('content')
<!--Start breadcrumb area paroller-->
<section class="breadcrumb-area">
    <div class="breadcrumb-area-bg" style="background-image: url(assets/images/breadcrumb/breadcrumb-1.jpg);"></div>
    <div class="container">
        <div class="row">
            <div class="col-xl-12">
                <div class="inner-content text-center">
                    <div class="title">
                       <h2>Question & Answer</h2>
                    </div>
                    <div class="breadcrumb-menu">
                        <ul>
                            <li><a href="index.html">Home</a></li>
                            <li><i class="fa fa-angle-right" aria-hidden="true"></i></li>
                            <li class="active">FAQ’s</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End breadcrumb area-->

<!--Start Faq Style1 Area-->
<section class="faq-style1-area">
    <div class="container">
        <div class="row">

            <div class="col-xl-12 text-right-rtl">
                <div class="faq-style1-content">
                    <div class="faq-style1_tab tabs-box">
                        <ul class="tab-buttons clearfix">
                            <li data-tab="#general-questions" class="tab-btn active-btn">General Questions</li>
                            <li data-tab="#company-service" class="tab-btn">Company & Service</li>
                        </ul>

                        <div class="tabs-content">
                            <div class="pattern-bg" style="background-image: url(assets/images/pattern/thm-pattern-2.png);"></div>
                            <!--Start Tab-->
                            <div class="tab active-tab" id="general-questions">
                                <ul class="accordion-box">
                                    <!-- Question 1 -->
                                    @foreach($general_faqs->take(1) as $faq)
                                    @if(!empty( $faq->{'question_'.app()->getLocale()}))
                                    <li class="accordion block active-block">
                                        <div class="acc-btn active">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>{{ $faq->{'question_'.app()->getLocale()} }}</h3>
                                        </div>
                                        <div class="acc-content current">
                                            <p>{{ $faq->{'answer_'.app()->getLocale()} }}</p>
                                        </div>
                                    </li>
                                    @endif
                                    @endforeach
                                    <!-- Question 2 -->
                                    @foreach($general_faqs->skip(1) as $faq)
                                    @if(!empty( $faq->{'question_'.app()->getLocale()}))
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>{{ $faq->{'question_'.app()->getLocale()} }}</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>{{ $faq->{'answer_'.app()->getLocale()} }}</p>
                                        </div>
                                    </li>
                                    @endif
                                    @endforeach
                                    <!-- Question 3 -->
                                    {{-- <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>What is regeneration?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Regeneration is the process of cleaning and recharging the resin in water softeners to restore their efficiency in removing hardness-causing minerals from water.</p>
                                        </div>
                                    </li>
                                    <!-- Question 4 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>What is in bottled water?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Bottled water typically contains purified or natural spring water with minerals that enhance taste and support hydration.</p>
                                        </div>
                                    </li>
                                    <!-- Question 5 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>What should I consider before buying?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>When purchasing water, consider factors such as source, purification process, packaging, and environmental impact to ensure it meets your needs.</p>
                                        </div>
                                    </li>
                                    <!-- Question 6 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>Do I need filtering or softening?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Filtering removes contaminants, while softening reduces minerals like calcium and magnesium. Your choice depends on your water quality and personal requirements.</p>
                                        </div>
                                    </li>
                                    <!-- Question 7 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>What does a typical RO system remove from water?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>A reverse osmosis (RO) system effectively removes impurities such as salts, chemicals, and heavy metals, providing clean and pure drinking water.</p>
                                        </div>
                                    </li> --}}
                                </ul>
                            </div>

                            <!--End Tab-->
                            <!--Start Tab-->
                            <div class="tab" id="company-service">
                                <ul class="accordion-box">
                                    <!-- Question 1 -->
                                    @foreach($service_faqs->take(1) as $faq)
                                    @if(!empty( $faq->{'question_'.app()->getLocale()}))
                                    <li class="accordion block active-block">
                                        <div class="acc-btn active">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>{{$faq->{'question_'. app()->getLocale()} }}</h3>
                                        </div>
                                        <div class="acc-content current">
                                            <p>{{$faq->{'answer_'. app()->getLocale()} }}</p>
                                        </div>
                                    </li>
                                    @endif
                                    @endforeach
                                    <!-- Question 2 -->
                                    @foreach($service_faqs->skip(1) as $faq)
                                    @if(!empty( $faq->{'question_'.app()->getLocale()}))
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>{{$faq->{'question_'. app()->getLocale()} }}</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>{{$faq->{'answer_'. app()->getLocale()} }}</p>
                                        </div>
                                    </li>
                                    @endif
                                    @endforeach
                                    <!-- Question 3 -->
                                    {{-- <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>Do I need filtering or softening?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Filtering removes impurities like chlorine and contaminants, while softening reduces minerals like calcium and magnesium. Choose based on your water quality and requirements.</p>
                                        </div>
                                    </li>
                                    <!-- Question 4 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>How much water should I drink?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Experts recommend drinking at least 2 liters of water daily, but individual needs may vary based on activity level, health, and environment.</p>
                                        </div>
                                    </li>
                                    <!-- Question 5 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>What is regeneration?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Regeneration is a process used in water softeners to clean and recharge the resin, maintaining its ability to remove hardness-causing minerals from water.</p>
                                        </div>
                                    </li>
                                    <!-- Question 6 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>What is in bottled water?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Bottled water is either purified or sourced from natural springs, containing essential minerals to enhance taste and support hydration.</p>
                                        </div>
                                    </li>
                                    <!-- Question 7 -->
                                    <li class="accordion block">
                                        <div class="acc-btn">
                                            <div class="icon-outer"><i class="icon-close"></i></div>
                                            <h3>What does a typical RO system remove from water?</h3>
                                        </div>
                                        <div class="acc-content">
                                            <p>Reverse osmosis (RO) systems effectively remove impurities such as salts, chlorine, heavy metals, and harmful contaminants, providing clean and safe drinking water.</p>
                                        </div>
                                    </li> --}}
                                </ul>
                            </div>

                            <!--End Tab-->
                        </div>

                    </div>

                </div>
            </div>

        </div>
    </div>
</section>
<!--End Faq Style1 Area-->

<!--Start Faq Form Area-->
<section class="faq-form-area">
    <div class="faq-form-area-bg" style="background-image: url(assets/images/parallax-background/faq-form-area-bg.jpg);"></div>
    <div class="container">
        <div class="row">

            <div class="col-xl-6">
                <div class="faq-left-box">
                    <div class="thm-round-box1 js-tilt paroller">
                        <h3>Our<br> Support<br> Center</h3>
                    </div>
                </div>
            </div>

            <div class="col-xl-6">
                <div class="contact-form-box1 faq-form-box">
                    <div class="top-title text-center">
                        <h2>Ask Your Question</h2>
                    </div>
                    <form id="contact-form" name="contact_form" class="default-form1" action="{{ route('user.message')}}" method="POST">
                        @csrf
                        <div class="input-box">
                            <input type="text" name="user_name" value="" placeholder="Your Name" required="">
                            <div class="icon">
                                <i class="fa fa-user" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <input type="email" name="email" value="" placeholder="Email Address" required="">
                            <div class="icon">
                                <i class="fa fa-envelope" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <div class="select-box">
                                <div class="round-shape"></div>
                                <select class="wide" name="question_category">
                                    <option value="" disabled selected>Select Category</option>
                                    <option value="general">General</option>
                                    <option value="service">Company & Service</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="icon">
                                <i class="fa fa-cog" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box">
                            <input type="text" name="address" value="" placeholder="Your Address">
                            <div class="icon">
                                <i class="fa fa-map-marker" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="input-box two">
                            <textarea name="questin" placeholder="Enter your Question..." required=""></textarea>
                            <div class="icon">
                                <i class="fa fa-text-width" aria-hidden="true"></i>
                            </div>
                        </div>
                        <div class="button-box text-center">
                            <button class="btn-one" type="submit" data-loading-text="Please wait...">
                                <span class="round"></span>
                                <span class="txt">Send</span>
                            </button>
                        </div>
                    </form>

                </div>
            </div>

        </div>
    </div>
</section>
<!--End Faq Form Area-->

@endsection
