<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\RecentSearch;

class CleanupOldSearches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'searches:cleanup {--days=90 : Number of days to keep}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old search records to keep the database optimized';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');

        $this->info("Cleaning up search records older than {$days} days...");

        $deletedCount = RecentSearch::where('created_at', '<', now()->subDays($days))->count();

        if ($deletedCount > 0) {
            RecentSearch::where('created_at', '<', now()->subDays($days))->delete();
            $this->info("Successfully deleted {$deletedCount} old search records.");
        } else {
            $this->info("No old search records found to delete.");
        }

        // Also clean up duplicate keywords (keep only the most recent one for each keyword)
        $this->info("Cleaning up duplicate keywords...");

        $duplicates = RecentSearch::selectRaw('keyword, COUNT(*) as count')
            ->groupBy('keyword')
            ->having('count', '>', 1)
            ->get();

        $duplicateCount = 0;
        foreach ($duplicates as $duplicate) {
            // Keep only the most recent record for each keyword
            $keepRecord = RecentSearch::where('keyword', $duplicate->keyword)
                ->orderBy('updated_at', 'desc')
                ->first();

            $deleteCount = RecentSearch::where('keyword', $duplicate->keyword)
                ->where('id', '!=', $keepRecord->id)
                ->delete();

            $duplicateCount += $deleteCount;
        }

        if ($duplicateCount > 0) {
            $this->info("Successfully removed {$duplicateCount} duplicate search records.");
        } else {
            $this->info("No duplicate search records found.");
        }

        $this->info("Search cleanup completed successfully!");
    }
}
