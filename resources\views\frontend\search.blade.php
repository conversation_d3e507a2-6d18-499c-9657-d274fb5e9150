<div id="search-popup" class="search-popup">
    <div class="close-search"><i class="icon-close"></i></div>
    <div class="popup-inner">
        <div class="overlay-layer"></div>
        <div class="search-form">
            <form method="GET" action="{{ route('universal.search') }}">
                <div class="form-group">
                    <fieldset>
                        <input type="search" class="form-control" name="q" value="" placeholder="Search Here" required >
                        <input type="submit" value="Search Now!" class="theme-btn style-four">
                    </fieldset>
                </div>
            </form>
            <h3>Recent Search Keywords</h3>
            <ul class="recent-searches">
                @php
                    $recentSearches = app(App\Http\Controllers\FrontendController::class)->getRecentSearches();
                @endphp
                @if($recentSearches->count() > 0)
                    @foreach($recentSearches->take(5) as $search)
                        <li>
                            <a href="{{ route('universal.search', ['q' => $search->keyword]) }}"
                               title="Searched {{ $search->total_searches }} time{{ $search->total_searches > 1 ? 's' : '' }}">
                                {{ ucfirst($search->keyword) }}
                                @if($search->total_searches > 1)
                                    <span class="search-count">({{ $search->total_searches }})</span>
                                @endif
                            </a>
                        </li>
                    @endforeach
                @else
                    <!-- Fallback to default keywords if no recent searches -->
                    <li><a href="{{ route('universal.search', ['q' => 'water quality']) }}">Water Quality</a></li>
                    <li><a href="{{ route('universal.search', ['q' => 'mineral']) }}">Mineral</a></li>
                    <li><a href="{{ route('universal.search', ['q' => 'bottle']) }}">Bottle</a></li>
                    <li><a href="{{ route('universal.search', ['q' => 'safety']) }}">Safety</a></li>
                    <li><a href="{{ route('universal.search', ['q' => 'plan']) }}">Plan</a></li>
                @endif
            </ul>

            <style>
            .search-popup .recent-searches .search-count {
                font-size: 12px;
                opacity: 0.7;
                margin-left: 5px;
            }
            .search-popup .recent-searches li a {
                position: relative;
                transition: all 0.3s ease;
            }
            .search-popup .recent-searches li a:hover .search-count {
                opacity: 1;
            }
            </style>
        </div>
    </div>
</div>
