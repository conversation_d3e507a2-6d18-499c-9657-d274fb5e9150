<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_goals', function (Blueprint $table) {
            $table->id();
            $table->string('tag_en');
            $table->string('tag_am');
            $table->string('title_en');
            $table->string('title_am');
            $table->text('content_en');
            $table->text('content_am');
            $table->text('detail_content_en')->nullable();
            $table->text('detail_content_am')->nullable();
            $table->string('featured_image');
            $table->string('slug');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_goals');
    }
};
