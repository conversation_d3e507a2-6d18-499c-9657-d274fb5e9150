<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class AboutList extends Model
{
    use Sluggable;
    protected $fillable = [
        'title_en',
        'title_am',
        'content_en',
        'content_am',
        'icon',

    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_en'
            ]
        ];
    }
}
